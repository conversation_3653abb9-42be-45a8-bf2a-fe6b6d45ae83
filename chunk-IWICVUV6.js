import { k as GROUP_COLORS, o as html } from "./chunk-I5YWVGZK.js";
import tabGroupUpdatorStyles from "./tabgroup-updator-BSQZAEDQ.css" with { type: "css" };

/**
 * Custom element for creating or updating a Chrome tab group.
 * Provides UI for selecting color and title, and handles group creation/updating.
 * 
 * @element tabgroup-updator
 */
class TabGroupUpdator extends HTMLElement {
  /**
   * Reference to the title input element.
   * @type {HTMLInputElement}
   */
  titleInput;

  /**
   * The tab group object, if updating an existing group.
   * @type {chrome.tabGroups.TabGroup | undefined}
   */
  tabGroup;

  /**
   * Array of tab IDs to group.
   * @type {number[]}
   */
  tabIds;

  /**
   * Selected color for the tab group.
   * @type {string}
   */
  color;

  /**
   * @param {chrome.tabGroups.TabGroup | undefined} tabGroup - Existing tab group (if updating).
   * @param {number[]} tabIds - IDs of tabs to group.
   */
  constructor(tabGroup, tabIds) {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [tabGroupUpdatorStyles];
    this.tabGroup = tabGroup;
    this.tabIds = tabIds;
    this.color = tabGroup?.color || GROUP_COLORS[0];
  }

  /**
   * Handles creation or update of the tab group.
   * Updates group title and color, then removes the UI.
   * @returns {Promise<void>}
   */
  async updateGroup() {
    try {
      // Use existing group ID or create a new group
      let groupId =
        this.tabGroup?.id ??
        (await chrome.tabs.group({
          tabIds: this.tabIds,
          createProperties: { windowId: globalThis.windowId },
        }));

      await chrome.tabGroups.update(groupId, {
        collapsed: false,
        title: this.titleInput.value,
        color: this.color,
      });

      // Remove the updater UI and any marked action
      this.remove();
      $("marked-action")?.remove();
    } catch (err) {
      console.error(err);
    }
  }

  /**
   * Handles color selection from the color picker.
   * @param {Event} event
   */
  onColorPick({ target }) {
    this.color = target.value;
  }

  /**
   * Renders the UI for the tab group updater.
   * @returns {DocumentFragment}
   */
  render() {
    /**
     * Renders a single color radio button.
     * @param {string} color
     * @returns {import('lit-html').TemplateResult}
     */
    const renderColorOption = (color) => html`<label style="--clr:${color}">
      <input type="radio" name="group-color" value="${color}" hidden />
    </label>`;

    return html`
      <div class="color-box" @change=${this.onColorPick.bind(this)}>
        ${GROUP_COLORS.map(renderColorOption)}
      </div>
      <input
        type="text"
        part="title-input"
        list="group_titles"
        value="${this.tabGroup?.title ?? ""}"
        ref=${(el) => (this.titleInput = el)}
        placeholder="${i18n("update_tabgroup_title")}" />
      <button @click=${this.updateGroup.bind(this)}>
        ${this.tabGroup ? "Update" : "Create"}
      </button>
      <datalist id="group_titles"></datalist>
    `;
  }

  /**
   * Lifecycle: called when element is added to the DOM.
   * Sets up UI, datalist, popover, and event listeners.
   */
  connectedCallback() {
    this.id = "tabgroup-updator";
    this.style.padding = "0.6em";
    this.setAttribute("popover", "");
    this.shadowRoot.replaceChildren(this.render());
    this.addDataList();
    this.showPopover?.();
    $on(this, "toggle", (evt) => evt.newState === "closed" && this.remove());
  }

  /**
   * Populates the datalist with existing group titles from storage.
   * @returns {Promise<void>}
   */
  async addDataList() {
    try {
      let { tabGroups } = await getStore("tabGroups");
      if (!tabGroups) return;
      let fragment = new DocumentFragment();
      for (let title of Object.keys(tabGroups)) {
        let option = document.createElement("option");
        option.value = title;
        fragment.appendChild(option);
      }
      this.shadowRoot.lastElementChild.appendChild(fragment);
    } catch (err) {
      console.error(err);
    }
  }
}

customElements.define("tabgroup-updator", TabGroupUpdator);
export { TabGroupUpdator as a };
