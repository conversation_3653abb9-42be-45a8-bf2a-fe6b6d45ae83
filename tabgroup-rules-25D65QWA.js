import { a as C } from "./chunk-I5MX6W5Q.js";
import {
  a as G,
  e as f,
  k as x,
  n as k,
  o as a,
  p as M,
} from "./chunk-BHCSV6LT.js";
import "./chunk-EK7ODJWE.js";
var b = class {
  constructor(e, t, i, o = "", n = "") {
    (this.id = "r" + Math.random().toString(36).slice(2)),
      (this.name = o),
      (this.ctmGroupTitle = n),
      (this.color = i ?? "grey"),
      (this.urlMatches = e ?? {}),
      (this.titleIncludes = t ?? []),
      (this.priority = 0),
      (this.enabled = !0),
      (this.createdAt = Date.now()),
      (this.lastModifiedAt = Date.now());
  }
};
var s = { TabGroupRules: "TabGroupRules" };
function D({ target: l }) {
  l.result.createObjectStore(s.TabGroupRules, { keyPath: "id" });
}
function c() {
  return new Promise((l, e) => {
    let t = indexedDB.open("tabVertikal", 1);
    (t.onupgradeneeded = D),
      (t.onsuccess = () => l(t.result)),
      (t.onerror = () => e(t.error)),
      (t.onblocked = () => console.warn("Pending until unblocked"));
  });
}
async function E() {
  return new Promise((l, e) => {
    c().then((t) => {
      let o = t
        .transaction(s.TabGroupRules, "readonly")
        .objectStore(s.TabGroupRules)
        .getAll();
      (o.onsuccess = ({ target: n }) => l(n.result)),
        (o.onerror = (n) => e(n)),
        t.close();
    });
  });
}
async function S(l) {
  return new Promise((e, t) => {
    c().then((i) => {
      let n = i
        .transaction(s.TabGroupRules, "readwrite")
        .objectStore(s.TabGroupRules)
        .put(l);
      (n.onsuccess = (r) => e(l)), (n.onerror = (r) => t(r)), i.close();
    });
  });
}
async function p(l, e, t) {
  return new Promise((i, o) => {
    c().then((n) => {
      let r = n
          .transaction(s.TabGroupRules, "readwrite")
          .objectStore(s.TabGroupRules),
        _ = r.get(l);
      (_.onsuccess = ({ target: m }) => {
        let v = m.result;
        (v[e] = t), (v.lastModifiedAt = Date.now());
        let w = r.put(v);
        (w.onsuccess = (T) => i(m.result)), (w.onerror = (T) => o(T));
      }),
        (_.onerror = (m) => o(m)),
        n.close();
    });
  });
}
async function I(l) {
  return new Promise((e, t) => {
    c().then((i) => {
      let n = i
        .transaction(s.TabGroupRules, "readwrite")
        .objectStore(s.TabGroupRules)
        .delete(l);
      (n.onsuccess = ({ target: r }) => e(r.result)),
        (n.onerror = (r) => t(r)),
        i.close();
    });
  });
}
var g = class {
  constructor() {}
  async getUrls() {
    let e = await G({});
    (this.tabUrls = e.map((t) => new URL(t.url))),
      (this.titles = e.map((t) => t.title));
  }
  topDomains() {
    return this.tabUrls.map((e) =>
      e.host.slice(e.host.lastIndexOf(".", e.host.lastIndexOf(".") - 1) + 1),
    );
  }
  domains() {
    return this.tabUrls.map((e) => e.host);
  }
  firstPathSegments() {
    return this.tabUrls.map((e) =>
      e.pathname.slice(1, e.pathname.indexOf("/", 1)),
    );
  }
  twoPathSegments() {
    return this.tabUrls.map((e) =>
      e.pathname.slice(
        1,
        e.pathname.indexOf("/", e.pathname.indexOf("/", 1) + 1),
      ),
    );
  }
  titleWords() {
    return this.titles.flatMap((e) => e.split(" ").filter((t) => t.length > 3));
  }
};
var h = class extends HTMLElement {
  constructor(e) {
    super(), (this.groupRule = e);
  }
  editGroupRule() {
    let e = new u(this.groupRule);
    this.parentElement.parentElement.appendChild(e);
  }
  async deleteGroupRule() {
    try {
      await I(this.groupRule.id), this.remove();
    } catch (e) {
      console.error(e);
    }
  }
  updateRuleProp = {
    name: ({ target: e }) => {
      p(this.groupRule.id, "name", e.value), (this.groupRule.name = e.value);
    },
    ctmGroupName: async ({ target: e }) => {
      p(this.groupRule.id, "ctmGroupName", e.value);
      let t = await f({ title: this.groupRule.ctmGroupTitle });
      (this.groupRule.ctmGroupTitle = e.value),
        t.length !== 0 && chrome.tabGroups.update(t[0].id, { title: e.value });
    },
    color: async ({ target: e }) => {
      p(this.groupRule.id, "color", e.value);
      let t = await f({ title: this.groupRule.ctmGroupTitle });
      (this.groupRule.color = e.value),
        t.length !== 0 && chrome.tabGroups.update(t[0].id, { color: e.value });
    },
  };
  toggleGroupRule({ target: e }) {
    p(this.groupRule.id, "enabled", e.value);
  }
  render() {
    let t = (() => {
      let i = [...Object.keys(this.groupRule.urlMatches)];
      return (
        this.groupRule.titleIncludes.length === 0 || i.push("title"),
        i.join(",")
      );
    })();
    return a`<input type="checkbox" name="" class="toggle_rule" @change=${this.toggleGroupRule.bind(this)} />
			<rule-details>
				<div class="left-column">
					<label>
						<span>${i18n("name")}:</span>
						<input type="text" value=${this.groupRule.name} @change=${this.updateRuleProp.name} />
					</label>
					<label>
						<span>${i18n("group")}:</span>
						<input
							type="text"
							value="${this.groupRule.ctmGroupTitle ?? "auto"}"
							@change=${this.updateRuleProp.ctmGroupName} />
					</label>
					<label>
						<span>${i18n("priority")}:</span>
						<input type="number" value=${this.groupRule.priority} @change=${this.updateRuleProp.priority} />
					</label>
				</div>
				<div class="center-column"></div>
				<div class="right-column">
					<div>
						<span>${i18n("matches")}:</span>
						<var title="${t}">${t.slice(0, 10)}</var>
					</div>
					<label>
						<span>${i18n("color")}:</span>
						<select id="color_select" value=${this.groupRule.color} @change=${this.updateRuleProp.color}>
							<option value="blue">blue</option>
							<option value="red">red</option>
							<option value="yellow">yellow</option>
							<option value="green">green</option>
							<option value="cyan">cyan</option>
							<option value="purple">purple</option>
							<option value="pink">pink</option>
							<option value="orange">orange</option>
							<option value="grey">grey</option>
						</select>
					</label>
					<div style="justify-content: end">
						<vt-icon
							ico="edit"
							title="${i18n("edit_rule")}"
							class="edit-icon"
							@click=${this.editGroupRule.bind(this)}></vt-icon>
						<vt-icon
							ico="delete"
							title="${i18n("delete_rule")}"
							class="delete-icon"
							@click=${this.deleteGroupRule.bind(this)}></vt-icon>
					</div>
				</div>
			</rule-details>`;
  }
  connectedCallback() {
    (this.id = this.groupRule.id),
      this.replaceChildren(this.render()),
      ($("#color_select", this).value = this.groupRule.color);
  }
};
customElements.define("tabgroup-rule", h);
async function P(l) {
  let e = new h(l),
    t = $("#r" + l.id, y);
  t ? t.replaceWith(e) : y.appendChild(e),
    (await getStore("autoGroupingOn")).autoGroupingOn ||
      chrome.runtime.sendMessage({
        msg: "toggle_auto_grouping",
        autoGroupOn: !0,
      });
}
var y,
  d = class extends HTMLElement {
    constructor() {
      super(), (y = this);
    }
    render(e) {
      return e.map((t) => new h(t));
    }
    async connectedCallback() {
      let e = await E();
      this.replaceChildren(...this.render(e));
    }
  };
customElements.define("tabgroup-rule-list", d);
var u = class extends HTMLDialogElement {
  constructor(e) {
    super(), (e ??= new b()), (this.rule = k(e));
  }
  setInputValue() {
    for (let e in this.rule.urlMatches)
      ($(`input[value="${e}"]`, this).checked = !0),
        ($(`input[name="${e}"]`, this).value =
          this.rule.urlMatches[e].join(","));
    $(`input[value="${this.rule.color}"]`, this).checked = !0;
  }
  async createRule() {
    let e = Object.assign({}, this.rule);
    e.urlMatches = {};
    for (let t in this.rule.urlMatches)
      e.urlMatches[t] = Object.assign([], this.rule.urlMatches[t]);
    e.titleIncludes = Object.assign([], this.rule.titleIncludes);
    try {
      await S(e), P(structuredClone(e)), this.remove();
    } catch (t) {
      console.error(t), document.body.appendChild(new C(t));
    }
  }
  removeMatchTitle({ currentTarget: currentTargetElement, target: targetElement }) {
    let i = targetElement.closest("li").textContent.trim(),
      o = this.rule.titleIncludes.indexOf(i);
    o !== -1 && this.rule.titleIncludes.splice(o, 1),
      targetElement.closest("vt-icon") ||
        (currentTargetElement.previousElementSibling.previousElementSibling.value = i);
  }
  addMatchTitle({ code: keyCode, target: inputElement }) {
    if (keyCode === "Enter") {
      this.rule.titleIncludes.push(inputElement.value);
      inputElement.value = "";
    }
  }
  onUrlMatchChange(event) {
    let inputElement = event.target;
    if (inputElement.type === "checkbox") {
      let i = (o) => {
        (o.disabled = inputElement.checked),
          inputElement.checked
            ? delete this.rule.urlMatches[o.value]
            : o.checked &&
              (this.rule.urlMatches[o.value] =
                o.parentElement.nextElementSibling.value.trim());
      };
      if (inputElement.value === "two_path_segment" || inputElement.value === "domain") {
        let o =
          event.target.closest("li").previousElementSibling.firstElementChild
            .firstElementChild;
        i(o);
      }
      if (inputElement.value === "ctm_match_pattern")
        for (let o of event.currentTarget.querySelectorAll(
          'input[name="url-match"]',
        ))
          o !== inputElement && i(o);
      if (inputElement.checked) {
        let o = event.target.parentElement.nextElementSibling.value.trim();
        this.rule.urlMatches[inputElement.value] = o ? o.split(",") : [];
      } else delete this.rule.urlMatches[inputElement.value];
    } else
      inputElement.previousElementSibling.firstElementChild.checked &&
        (this.rule.urlMatches[event.target.name] = inputElement.value.trim()
          ? inputElement.value.split(",")
          : []);
  }
  onColorChange({ target: colorInput }) {
    this.rule.color = colorInput.value;
  }
  render(context) {
    let renderChip = (title) =>
        a`<li class="chip-item"><span>${title}</span><vt-icon ico="close" title="remove"></vt-icon></li>`,
      renderColorOption = (color) => a`<label style="--clr:${color}">
			<input type="radio" name="group-color" value="${color}" hidden />
		</label>`,
      renderDatalistOption = (option) => a`<option value="${option}"></option>`;
    return a`<vt-icon ico="close-circle" class="close-btn" @click=${this.remove.bind(this)}></vt-icon>
			<label>
				<span>${i18n("rule_name")}</span>
				<input type="text" .value=${() => this.rule.name} />
			</label>

			<section class="title-match" style="margin-top:0.8em">
				<label>
					<span><vt-icon ico="title"></vt-icon> ${i18n("tab_title_includes")}</span>
					<input type="text" list="tabTitles" @keyup=${this.addMatchTitle.bind(this)} />
				</label>
				<datalist id="tabTitles">${context.titleWords().map(renderDatalistOption)}</datalist>
				<ul class="chip-list" @click=${this.removeMatchTitle.bind(this)}>
					${M(this.rule.titleIncludes, renderChip)}
				</ul>
			</section>
			<section class="url-match">
				<header><vt-icon ico="route"></vt-icon> <span>${i18n("url_match")}</span></header>
				<ul @change=${this.onUrlMatchChange.bind(this)}>
					<li>
						<label> <input type="checkbox" name="url-match" value="top_domain" />${i18n("top_domain")}</label>

						<input
							type="email"
							name="top_domain"
							list="top_domains"
							placeholder="${i18n("enter_selected_top_domains")}" />
						<datalist id="top_domains">${context.topDomains().map(renderDatalistOption)}</datalist>
					</li>
					<li>
						<label><input type="checkbox" name="url-match" value="domain" />${i18n("domain")} </label>
						<input type="email" name="domain" list="domains" placeholder="${i18n("enter_selected_domains")}" />
						<datalist id="domains">${context.domains().map(renderDatalistOption)}</datalist>
					</li>
					<li>
						<label>
							<input type="checkbox" name="url-match" value="first_path_segment" /> ${i18n("first_path_segment")}
						</label>
						<input
							type="email"
							name="first_path_segment"
							list="first_path_segments"
							placeholder="${i18n("selected_first_path")}" />
						<datalist id="first_path_segments">${context.firstPathSegments().map(renderDatalistOption)}</datalist>
					</li>
					<li>
						<label>
							<input type="checkbox" name="url-match" value="two_path_segment" /> ${i18n("two_path_segment")}
						</label>
						<input
							type="email"
							name="two_path_segment"
							list="two_path_segments"
							placeholder="${i18n("selected_first_two_path")}" />
						<datalist id="two-path-segments">${context.twoPathSegments().map(renderDatalistOption)}</datalist>
					</li>
					<li>
						<label>
							<input type="checkbox" name="url-match" value="ctm_match_pattern" />
							<span>
								${i18n("custom_url")}
								<a href="https://developer.chrome.com/docs/extensions/develop/concepts/match-patterns">
									${i18n("match_patterns")}
								</a>
							</span>
						</label>
						<input type="email" name="ctm_match_pattern" placeholder="https://*/foo*,http://google.es/*" />
					</li>
				</ul>
			</section>

			<section class="rule-row">
				<label>
					<span>${i18n("custom_tabgroup_title")}</span>
					<input
						type="text"
						.value=${() => this.rule.ctmGroupTitle}
						placeholder="(optional) custom title"
						style="width:80%" />
				</label>
				<label>
					<span>${i18n("priority")}</span>
					<input type="number" .value=${() => this.rule.priority} />
				</label>
			</section>

			<section>
				<span style="margin-bottom:0.4em"><vt-icon ico="color"></vt-icon> ${i18n("color")}</span>
				<div class="color-pot" @change=${this.onColorChange.bind(this)}>${x.map(renderColorOption)}</div>
			</section>
			<button @click=${this.createRule.bind(this)}>${i18n("create_rule")}</button>`;
  }
  async connectedCallback() {
    this.id = "create-rule-dialog";
    let e = new g();
    await e.getUrls(),
      this.replaceChildren(this.render(e)),
      this.showModal(),
      this.setInputValue();
  }
};
customElements.define("create-rule-dialog", u, { extends: "dialog" });
import O from "./tabgroup-rules-GCDCIF3A.css" with { type: "css" };
var R = class extends HTMLElement {
  constructor() {
    super(),
      this.attachShadow({ mode: "open" }),
      (this.shadowRoot.adoptedStyleSheets = [O]);
  }
  showRuleCreator() {
    let e = new u();
    this.shadowRoot.firstElementChild.appendChild(e);
  }
  render() {
    return [
      a`<header>
			<vt-icon ico="close-circle" class="close-btn" @click=${this.remove.bind(this)}></vt-icon>
			<span><vt-icon ico="group-rule"></vt-icon> ${i18n("tabgroup_rules")}</span>
			<button @click=${this.showRuleCreator.bind(this)}><vt-icon ico="plus"></vt-icon> ${i18n("add_rule")}</button>
		</header>`,
      new d(),
    ];
  }
  connectedCallback() {
    let e = document.createElement("dialog");
    e.append(...this.render()), this.shadowRoot.appendChild(e), e.showModal();
  }
};
customElements.define("tabgroup-rule-dialog", R);
export { R as TabGroupRuleDialog };
