import "./chunk-EK7ODJWE.js";
import emojiPickerStyles from "./emoji-picker-KRZLCODK.css" with { type: "css" };

/**
 * EmojiPicker is a custom HTML element that provides a UI for selecting emojis.
 * It fetches emoji data, displays categories, and allows users to insert emojis into an input.
 * 
 * @element emoji-picker
 */
class EmojiPicker extends HTMLElement {
  /**
   * Stores the emoji data categorized by type.
   * @type {Record<string, string[]>}
   */
  emojis = {};

  constructor() {
    super();
    // Attach shadow DOM and apply styles
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [emojiPickerStyles];
  }

  /**
   * Renders the HTML structure for the emoji picker.
   * @returns {string} HTML string for the picker UI
   */
  render() {
    return `
      <header class="category-bar">
        <div data-ctg="animals">\u{1F436}</div>
        <div data-ctg="food_drinks">\u{1F355}</div>
        <div data-ctg="travels">\u{1F680}</div>
        <div data-ctg="objects">\u{1F389}</div>
        <div data-ctg="symbols">\u2764\uFE0F</div>
        <div data-ctg="flags">\u{1F3C1}</div>
      </header>
      <article class="emoji-container">
        <div class="emoji-category" id="animals"></div>
        <div class="emoji-category" id="food_drinks"></div>
        <div class="emoji-category" id="travels"></div>
        <div class="emoji-category" id="objects"></div>
        <div class="emoji-category" id="symbols"></div>
        <div class="emoji-category" id="flags"></div>
      </article>
    `;
  }

  /**
   * Populates the emoji container for a given category.
   * If already populated, just scrolls into view.
   * @param {string} category - The emoji category to show
   */
  showCategoryEmoji(category) {
    const categoryContainer = this.shadowRoot.lastElementChild.querySelector(`#${category}`);
    categoryContainer.scrollIntoView();
    if (categoryContainer.hasChildNodes()) return;

    const emojisForCategory = this.emojis[category];
    let count = emojisForCategory.length;
    const fragment = new DocumentFragment();

    // Add each emoji as a span element
    while (count--) {
      const emojiSpan = document.createElement("span");
      emojiSpan.textContent = emojisForCategory[count];
      fragment.appendChild(emojiSpan);
    }
    categoryContainer.appendChild(fragment);
  }

  /**
   * Fetches emoji data from a remote API and stores it.
   * @returns {Promise<Record<string, string[]>>} The emoji data
   */
  async fetchEmoji() {
    const response = await fetch("https://api.npoint.io/ac2f15a998f82653d938", {
      mode: "cors",
    });
    const emojiData = response.ok && (await response.json());
    setStore({ emojis: emojiData });
    return emojiData;
  }

  /**
   * Lifecycle callback when the element is added to the DOM.
   * Initializes the picker, fetches emoji data, and sets up event listeners.
   */
  async connectedCallback() {
    this.id = "emoji-picker";
    this.setAttribute("popover", "");
    this.shadowRoot.innerHTML = this.render();

    // Retrieve emoji data from store or fetch if not present
    const { emojis } = await getStore("emojis");
    this.emojis = emojis || (await this.fetchEmoji());

    // Show the default category and popover
    this.showCategoryEmoji("animals");
    this.showPopover();

    /**
     * Handles emoji selection: inserts the emoji into the previous input element.
     * @param {PointerEvent} event
     */
    const handleEmojiClick = ({ target }) => {
      if (target.nodeName === "SPAN") {
        this.previousElementSibling.value = target.textContent + this.previousElementSibling.value;
      }
    };

    /**
     * Handles category selection: shows the selected emoji category.
     * @param {MouseEvent} event
     */
    const handleCategoryClick = ({ target }) => {
      if (target.nodeName === "DIV") {
        this.showCategoryEmoji(target.dataset.ctg);
      }
    };

    // Listen for emoji selection
    $on(this.shadowRoot.lastElementChild, "pointerdown", handleEmojiClick);
    // Listen for category selection
    $on(this.shadowRoot.firstElementChild, "click", handleCategoryClick);
  }

  /**
   * Shows the popover UI for the emoji picker.
   * (Assumed to be implemented elsewhere or as a mixin.)
   */
  showPopover() {
    // Implementation assumed to be provided by another script or mixin.
  }
}

customElements.define("emoji-picker", EmojiPicker);
export { EmojiPicker };
