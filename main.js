/**
 * TabVertikal Chrome Extension - Main Entry Point
 * A comprehensive tab management extension with advanced features for organizing,
 * searching, grouping, and managing browser tabs across multiple windows.
 *
 * @fileoverview Main application entry point with global utilities, keyboard shortcuts,
 * and core tab management functionality.
 */

// Import core utilities and components from chunk modules
import {
  queryTabs,
  createTab,
  removeTabs,
  TAB_GROUP_COLORS as groupColors,
  html as templateLiteral,
} from "./chunk-I5YWVGZK.js";

import {
  withGlobalSetup,
  withI18n,
  checkAutoGroupRule,
  setSetting,
} from "./chunk-BHCSV6LT.js";

/**
 * Global utility setup for DOM, events, i18n, storage, and notifications.
 * Provides commonly used functions as global utilities for the extension.
 */
const globalSetup = withGlobalSetup((i18nContext) => {
  /**
   * Get element by ID.
   * @type {(id: string) => HTMLElement}
   */
  globalThis.getElementById = document.getElementById.bind(document);

  /**
   * Fire a custom event on an element.
   * @type {(el: HTMLElement, event: string, detail?: any) => void}
   */
  globalThis.fireEvent = (element, eventName, detail) =>
    element.dispatchEvent(detail ? new CustomEvent(eventName, { detail }) : new CustomEvent(eventName));

  /**
   * Add event listener.
   * @type {(el: HTMLElement, event: string, handler: EventListener) => void}
   */
  globalThis.addEvent = (element, eventName, handler) => element.addEventListener(eventName, handler);

  /**
   * Add one-time event listener.
   * @type {(el: HTMLElement, event: string, handler: EventListener) => void}
   */
  globalThis.addEventOnce = (element, eventName, handler) => element.addEventListener(eventName, handler, { once: true });

  /**
   * Query selector shortcut.
   * @type {(selector: string, root?: Document|HTMLElement) => HTMLElement}
   */
  globalThis.querySelector = (selector, root) => (root || document).querySelector(selector);

  /**
   * Internationalization message getter.
   * @type {(msg: string) => string}
   */
  globalThis.i18n = chrome.i18n.getMessage.bind(i18nContext);

  /**
   * Chrome local storage get/set.
   */
  globalThis.getStore = chrome.storage.local.get.bind(chrome.storage.local);
  globalThis.setStore = chrome.storage.local.set.bind(chrome.storage.local);

  /**
   * Toast notification for user feedback.
   * @type {(msg: string) => void}
   */
  const snackbar = getElementById("snackbar");
  globalThis.toast = (message) => {
    snackbar.hidden = false;
    snackbar.innerText = message;
    setTimeout(() => (snackbar.hidden = true), 5100);
  };
});
withI18n(globalSetup);

import icons from "/assets/icons.json" with { type: "json" };

/**
 * Custom element for displaying SVG icons.
 * Supports toggle state and checked state.
 */
/**
 * Custom element for displaying SVG icons.
 * Supports toggle state and checked state.
 * @element vt-icon
 */
class VtIconElement extends HTMLElement {
  /**
   * @param {string} [iconName]
   */
  constructor(iconName) {
    super();
    if (iconName) this.setAttribute("ico", iconName);
  }

  /**
   * Whether the icon is checked (for toggle icons).
   * @returns {boolean}
   */
  get checked() {
    return this._internals?.states.has("checked");
  }

  /**
   * Set checked state.
   * @param {boolean} value
   */
  set checked(value) {
    if (!this._internals) return;
    if (value) {
      this._internals.states.add("checked");
    } else {
      this._internals.states.delete("checked");
    }
  }

  /**
   * Set the icon by name.
   * @param {string} iconName
   */
  set ico(iconName) {
    if (this.firstElementChild) {
      this.firstElementChild.innerHTML = icons[iconName];
    }
  }

  /**
   * Render the SVG for the icon.
   * @param {string} iconName
   * @returns {string}
   */
  render(iconName) {
    return `<svg viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">${icons[iconName]}</svg>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.innerHTML = this.render(this.getAttribute("ico"));
    if (this.hasAttribute("toggle")) {
      this._internals = this.attachInternals();
      this.addEventListener("click", this.#onToggle.bind(this));
    }
  }

  /**
   * Toggle checked state and fire change event.
   * @private
   */
  #onToggle() {
    this.checked = !this.checked;
    this.dispatchEvent(new Event("change"));
  }
}
customElements?.define("vt-icon", VtIconElement);
/**
 * Set the color of a tab group element.
 * @param {HTMLElement} element - The element to style.
 * @param {string} color - The group color name.
 */
function setTabGroupColor(element, color) {
  element?.style.setProperty("--grp-clr", groupColors[color ?? "grey"]);
}

/**
 * Open Chrome's tab discard manager.
 */
function openTabDiscardManager() {
  chrome.tabs.create({ url: "chrome://discards/" });
}

/**
 * Open the extension's options page.
 */
function openOptionsPage() {
  chrome.runtime.openOptionsPage();
}

/**
 * Show the Tab Group Rule dialog.
 */
async function showTabGroupRuleDialog() {
  const { TabGroupRuleDialog } = await import("./tabgroup-rules-6PBAL65H.js");
  document.body.appendChild(new TabGroupRuleDialog());
}

/**
 * Group all ungrouped tabs, or notify if no rule exists.
 */
async function groupAllUngroupedTabs() {
  if (!(await checkAutoGroupRule()))
    return notify(i18n("create_autogroup_rule_first"), "error");
  await chrome.runtime.sendMessage("group_all_ungroup_tabs");
}

/**
 * Get the index of the currently active tab in the current window.
 * @returns {Promise<number>}
 */
async function getActiveTabIndex() {
  return (await queryTabs({ windowId: globalThis.windowId, active: true }))[0].index;
}

/**
 * Create a new tab at the current tab's index.
 */
async function createTabAtCurrentIndex() {
  createTab({ index: await getActiveTabIndex() });
}

/**
 * Create a new tab after the current tab.
 */
async function createTabAfterCurrent() {
  createTab({ index: (await getActiveTabIndex()) + 1 });
}

/**
 * Close all tabs above the current tab.
 */
async function closeTabsAbove() {
  const currentIndex = await getActiveTabIndex();
  const tabsToClose = (await queryTabs({ windowId })).slice(0, currentIndex).map(tab => tab.id);
  chrome.tabs.remove(tabsToClose).catch(console.error);
}

/**
 * Close all tabs below the current tab.
 */
async function closeTabsBelow() {
  const currentIndex = await getActiveTabIndex();
  const tabsToClose = (await queryTabs({ windowId })).slice(currentIndex).map(tab => tab.id);
  chrome.tabs.remove(tabsToClose).catch(console.error);
}

/**
 * Close all tabs except the active one.
 */
async function closeOtherTabs() {
  const tabsToClose = (await queryTabs({ windowId })).filter(tab => !tab.active).map(tab => tab.id);
  chrome.tabs.remove(tabsToClose).catch(console.error);
}

/**
 * Get the ID of the currently active tab in the current window.
 * @returns {Promise<number>}
 */
async function getActiveTabId() {
  return (await queryTabs({ windowId: globalThis.windowId, active: true }))[0].id;
}

/**
 * Duplicate the currently active tab.
 */
async function duplicateActiveTab() {
  chrome.tabs.duplicate(await getActiveTabId());
}

/**
 * Discard (suspend) the currently active tab.
 */
async function discardActiveTab() {
  chrome.tabs.discard(await getActiveTabId());
}

/**
 * Show the Tab Group selection dialog for a tab.
 * @param {number} [tabId]
 */
async function showTabGroupSelect(tabId) {
  tabId ??= await getActiveTabId();
  const { TabgroupSelect } = await import("./tabgroup-select-KETNIX4M.js");
  document.body.appendChild(new TabgroupSelect([tabId]));
}

/**
 * Show the Window selection dialog for a tab.
 * @param {number} [tabId]
 */
async function showWindowSelect(tabId) {
  tabId ??= await getActiveTabId();
  const { WindowSelect } = await import("./window-select-CYH65ETX.js");
  document.body.appendChild(new WindowSelect([tabId]));
}

/**
 * Remove duplicate tabs in the current window.
 */
async function removeDuplicateTabs() {
  const tabs = await chrome.tabs.query({ windowId: globalThis.windowId });
  const urlToId = {};
  const duplicateIds = new Set();
  tabs.reduce((acc, tab) => {
    if (acc[tab.url]) {
      duplicateIds.add(tab.id);
    } else {
      acc[tab.url] = tab.id;
    }
    return acc;
  }, urlToId);
  if (duplicateIds.size !== 0) {
    chrome.tabs.remove([...duplicateIds]).catch(console.error);
  }
}
/**
 * Remove all tabs from a specific domain.
 * @param {Event} event
 */
function removeTabsByDomain({ currentTarget }) {
  const domain = currentTarget.dataset.domain;
  if (!domain) return;
  const idsToRemove = [];
  querySelector("tab-container").tabs.filter(tab =>
    new URL(tab.url).host !== domain ? true : (idsToRemove.push(tab.id), false)
  );
  removeTabs(idsToRemove);
  currentTarget.remove();
}

/**
 * Suspend (discard) all tabs except the active one in the current window.
 */
function suspendOtherTabs() {
  chrome.tabs
    .query({ currentWindow: true })
    .then(tabs =>
      tabs.forEach(({ active, id }) => {
        if (!active) chrome.tabs.discard(id);
      })
    )
    .catch(console.error);
}

/**
 * Handle paste event for URLs: activate if open, otherwise open in new tab.
 * @param {ClipboardEvent} event
 */
async function handlePasteUrl(event) {
  const url =
    event.clipboardData.getData("text/uri-list") ||
    event.clipboardData.getData("text/plain");
  if (url.startsWith("http") && URL.canParse(url)) {
    const existingTab = (await queryTabs({ windowId, url }))[0];
    if (existingTab) {
      chrome.tabs.update(existingTab.id, { active: true });
    } else {
      createTab({ url, index: await getActiveTabIndex() });
    }
    event.preventDefault();
  }
}

/**
 * Save a tab group's color to persistent storage.
 * @param {{title: string, color: string}} group
 */
async function saveTabGroupColor(group) {
  try {
    const store = (await getStore("tabGroups")).tabGroups || {};
    if (store[group.title]) return;
    store[group.title] = group.color;
    await setStore({ tabGroups: store });
  } catch (err) {
    console.error(err);
  }
}

/**
 * Keyboard shortcut handler for the extension.
 */
class KeyboardShortcuts {
  constructor() {
    addEventListener("keydown", this.keyUpListener.bind(this));
  }

  altKeys = {
    KeyN: createTabAfterCurrent,
    KeyD: duplicateActiveTab,
    KeyH: discardActiveTab,
    KeyG: showTabGroupSelect,
    KeyM: showWindowSelect,
  };

  ctrlShiftKeys = {
    KeyH: suspendOtherTabs,
    KeyE: async () => {
      const { ImportExportDialog } = await import("./import-export-dialog-G6WE4YFT.js");
      this.importExportDialog = new ImportExportDialog();
      querySelector("window-bar").shadowRoot.appendChild(this.importExportDialog);
    },
    keyQ: closeTabsAbove,
    KeyF: () =>
      querySelector('input[type="search"]', querySelector("window-bar").shadowRoot).focus(),
  };

  ctrlAltKeys = {
    KeyC: () => {
      const tabContainer = querySelector("tab-container");
      tabContainer.toggleAttribute("compact");
      setSetting({ compactMode: tabContainer.hasAttribute("compact") });
    },
    KeyD: removeDuplicateTabs,
    KeyO: openOptionsPage,
    KeyN: createTabAtCurrentIndex,
    KeyR: showTabGroupRuleDialog,
    keyS: openTabDiscardManager,
    KeyW: closeTabsBelow,
    KeyQ: closeOtherTabs,
  };

  /**
   * Main keyup event handler for shortcuts.
   * @param {KeyboardEvent} event
   */
  keyUpListener(event) {
    if (event.ctrlKey) {
      if (event.shiftKey) {
        this.ctrlShiftKeys[event.code]?.();
      } else if (event.altKey || event.metaKey) {
        this.ctrlAltKeys[event.code]?.();
      }
    } else if (event.altKey || event.metaKey) {
      this.altKeys[event.code]?.();
    }
  }
}

// Initialize keyboard shortcuts after 1 second.
setTimeout(() => new KeyboardShortcuts(), 1000);
import Rt from "./alert-box-QPHCQPTR.css" with { type: "css" };
/**
 * AlertBoxElement - Custom element for displaying alert notifications.
 * Shows a popover with a message and style (success/error).
 * @class
 * @extends HTMLElement
 */
class AlertBoxElement extends HTMLElement {
  /**
   * Initializes the alert box and attaches shadow DOM.
   */
  constructor() {
    super();
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [Rt];
  }

  /**
   * Show the alert box with a message and type.
   * @param {string} message - The message to display.
   * @param {string} [type="success"] - The type of alert ("success" or "error").
   */
  show = (message, type = "success") => {
    this.box.className = type;
    this.box.children[1].textContent = message;
    this.showPopover();
    setTimeout(() => this.hidePopover(), 4100);
  };

  /**
   * Render the alert box HTML.
   * @returns {string}
   */
  render() {
    return `<div>
      <svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="white" d='M10,17L5,12L6.41,10.58L10,14.17L17.59,6.58L19,8M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z' /></svg>
      <span class="notice-txt"></span>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="white" d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" /></svg>
    </div>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.id = "alert-box";
    this.setAttribute("popover", "");
    this.shadowRoot.innerHTML = this.render();
    this.box = this.shadowRoot.firstElementChild;
    this.box.lastElementChild.addEventListener("click", () => this.hidePopover());
  }
}
customElements.define("alert-box", AlertBoxElement);

// Singleton instance for global notifications
const globalAlertBox = new AlertBoxElement();
document.body.appendChild(globalAlertBox);
globalThis.notify = globalAlertBox.show;
/**
 * Extension manifest details for error reporting.
 * @type {{ version: string, short_name: string, update_url: string }}
 */
const {
  version: extensionVersion,
  short_name: extensionShortName,
  update_url: extensionUpdateUrl,
} = chrome.runtime.getManifest();

/**
 * Indicates if the extension is in development mode (no update URL).
 * @type {boolean}
 */
const isDevelopmentMode = !extensionUpdateUrl;

/**
 * Reports errors to a remote bug collector service.
 * @param {ErrorEvent|PromiseRejectionEvent} errorEvent
 */
function reportError(errorEvent) {
  if (isDevelopmentMode) return console.error(errorEvent);
  const endpoint = "https://bug-collector.noterail.workers.dev/collect-bug";
  const payload = {
    id: 1,
    extId: chrome.runtime.id,
    extName: extensionShortName,
    extVersion: extensionVersion,
    message: errorEvent.message ?? errorEvent.reason,
    stack: errorEvent.stack,
    browserOs: navigator.userAgent,
    catchedAt: new Date().toISOString(),
  };
  const request = new Request(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  });
  fetch(request)
    .then((response) => response.text())
    .then((text) => console.log(text))
    .catch((err) => console.log(err));
}

// Attach global error handlers in production mode.
if (!isDevelopmentMode) {
  self.addEventListener("error", reportError);
  self.addEventListener("unhandledrejection", reportError);
  // Proxy console.error to also report errors.
  const errorProxyHandler = {
    apply: function (target, thisArg, argumentsList) {
      reportError(argumentsList[0]);
      return target.call(thisArg, ...argumentsList);
    },
  };
  console.error = new Proxy(console.error, errorProxyHandler);
}

/**
 * FilterTabsElement - Custom element for filtering tabs by domain.
 * Displays a list of domains for the current window's tabs.
 * @class
 * @extends HTMLElement
 */
class FilterTabsElement extends HTMLElement {
  /**
   * Initializes the filter tabs element.
   */
  constructor() {
    super();
    /** @type {string[]} */
    this.domains = [];
  }

  /**
   * Fetches unique domains from all tabs in the current window.
   * @returns {Promise<string[]>}
   */
  async fetchDomains() {
    const tabs = await r({ windowId: globalThis.windowId });
    const domains = tabs.map(tab => new URL(tab.url).host);
    return [...new Set(domains)];
  }

  /**
   * Renders a domain list item.
   * @param {string} domain
   * @returns {any}
   */
  domainItem = (domain) => c`<li data-domain=${domain} @click=${It}>
    <span>${domain}</span>
    <vt-icon ico="delete-cross"></vt-icon>
  </li>`;

  /**
   * Renders the domain list.
   * @param {string[]} domains
   * @returns {any[]}
   */
  render(domains) {
    return domains.map(this.domainItem);
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.id = "filter-tabs";
    this.style.top = "13em";
    this.setAttribute("popover", "");
    $on(this, "toggle", (event) => {
      if (event.newState === "open") {
        this.fetchDomains().then((domains) =>
          this.replaceChildren(...this.render(domains))
        );
      }
    });
    this.showPopover();
  }
}
customElements.define("filter-tabs", FilterTabsElement);
/**
 * TabSortUtils - Utility class for sorting browser tabs by various criteria.
 * Provides static comparator functions and a main sorting dispatcher.
 */
class TabSortUtils {
  /**
   * Sort tabs by domain name (A-Z).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byDomainAZ(a, b) {
    const domainA = new URL(a.url).host;
    const domainB = new URL(b.url).host;
    return domainA.localeCompare(domainB);
  }

  /**
   * Sort tabs by domain name (Z-A).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byDomainZA(a, b) {
    const domainA = new URL(a.url).host;
    const domainB = new URL(b.url).host;
    return domainB.localeCompare(domainA);
  }

  /**
   * Sort tabs by URL (A-Z).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byUrlAZ(a, b) {
    return a.url.localeCompare(b.url);
  }

  /**
   * Sort tabs by URL (Z-A).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byUrlZA(a, b) {
    return b.url.localeCompare(a.url);
  }

  /**
   * Sort tabs by title (A-Z).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTitleAZ(a, b) {
    return a.title.localeCompare(b.title);
  }

  /**
   * Sort tabs by title (Z-A).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTitleZA(a, b) {
    return b.title.localeCompare(a.title);
  }

  /**
   * Sort tabs by last accessed time (oldest to newest).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTimeAZ(a, b) {
    return (a.lastAccessed ?? 0) - (b.lastAccessed ?? 0);
  }

  /**
   * Sort tabs by last accessed time (newest to oldest).
   * @param {chrome.tabs.Tab} a
   * @param {chrome.tabs.Tab} b
   * @returns {number}
   */
  static byTimeZA(a, b) {
    return (b.lastAccessed ?? 0) - (a.lastAccessed ?? 0);
  }

  /**
   * Placeholder for group sorting (not implemented).
   * @returns {number}
   */
  static byGroupAZ() {
    return 0;
  }

  /**
   * Sorts and reorders tabs in the browser window.
   * @param {chrome.tabs.Tab[]} tabs - Array of tab objects to sort.
   * @param {string} sortType - Sorting criteria identifier.
   */
  static async sortTabs(tabs, sortType) {
    switch (sortType) {
      case "domainAZ":
        tabs.sort(TabSortUtils.byDomainAZ);
        break;
      case "domainZA":
        tabs.sort(TabSortUtils.byDomainZA);
        break;
      case "titleAZ":
        tabs.sort(TabSortUtils.byTitleAZ);
        break;
      case "titleZA":
        tabs.sort(TabSortUtils.byTitleZA);
        break;
      case "timeAZ":
        tabs.sort(TabSortUtils.byTimeAZ);
        break;
      case "timeZA":
        tabs.sort(TabSortUtils.byTimeZA);
        break;
      case "reverse":
        tabs.reverse();
        break;
      // Add more sort types as needed
    }
    // Move each tab to its new index
    const movePromises = tabs.map((tab, idx) =>
      chrome.tabs.move(tab.id, { index: idx })
    );
    try {
      await Promise.all(movePromises);
    } catch (err) {
      notify("cannot sort tabs", "error");
    }
  }
}

/**
 * SortTabsElement - Custom element for displaying and handling tab sorting options.
 * Renders a popover menu with sorting choices and applies the selected sort.
 * @element sort-tabs
 */
class SortTabsElement extends HTMLElement {
  constructor() {
    super();
  }

  /**
   * Handles click events on sorting options and triggers tab sorting.
   * @param {Event} event
   */
  async handleSortClick({ target }) {
    // Fetch all tabs in the current window
    const tabs = await r({ windowId: globalThis.windowId });
    // Determine sort type from clicked list item's id
    const sortType = target.closest("li").id;
    await TabSortUtils.sortTabs(tabs, sortType);
  }

  /**
   * Renders the sorting options menu.
   * @returns {any}
   */
  render() {
    return c`
      <li id="domainAZ">
        <vt-icon ico="domain"></vt-icon>
        <span>Domain</span>
        <vt-icon ico="asc"></vt-icon>
      </li>
      <li id="domainZA">
        <vt-icon ico="domain"></vt-icon>
        <span>Domain</span>
        <vt-icon ico="desc"></vt-icon>
      </li>
      <li id="titleAZ">
        <vt-icon ico="web"></vt-icon>
        <span>Title</span>
        <vt-icon ico="asc"></vt-icon>
      </li>
      <li id="titleZA">
        <vt-icon ico="web"></vt-icon>
        <span>Title</span>
        <vt-icon ico="desc"></vt-icon>
      </li>
      <li id="timeAZ">
        <vt-icon ico="recent"></vt-icon>
        <span>${i18n("recent_used")}</span>
        <vt-icon ico="asc"></vt-icon>
      </li>
      <li id="timeZA">
        <vt-icon ico="recent"></vt-icon>
        <span>${i18n("recent_used")}</span>
        <vt-icon ico="desc"></vt-icon>
      </li>
      <li id="reverse">
        <vt-icon ico="reverse"></vt-icon>
        <span>${i18n("reverse")}</span>
        <vt-icon ico="reverse"></vt-icon>
      </li>
    `;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.id = "sort-tabs";
    this.style.top = "6.5em";
    this.setAttribute("popover", "");
    this.replaceChildren(this.render());
    this.showPopover();
    $on(this, "click", this.handleSortClick);
  }
}
customElements.define("sort-tabs", SortTabsElement);
/**
 * MoreMenuElement - Custom element for the "More" menu popup.
 * Provides actions for tab creation, sorting, filtering, settings, and more.
 * @class
 * @extends HTMLElement
 */
class MoreMenuElement extends HTMLElement {
  /**
   * Reference to the tab container element.
   * @type {HTMLElement|null}
   */
  tabContainer = null;

  /**
   * Reference to the filter tabs popover element.
   * @type {FilterTabsElement|null}
   */
  filterTabsPopover = null;

  /**
   * Reference to the sort tabs popover element.
   * @type {SortTabsElement|null}
   */
  sortTabsPopover = null;

  /**
   * Reference to the import/export dialog element.
   * @type {HTMLElement|null}
   */
  importExportDialog = null;

  constructor() {
    super();
  }

  /**
   * Toggles compact mode for the tab container and persists the setting.
   */
  toggleCompactMode() {
    if (!this.tabContainer) {
      this.tabContainer = document.querySelector("tab-container");
    }
    if (this.tabContainer) {
      this.tabContainer.toggleAttribute("compact");
      setSetting({ compactMode: this.tabContainer.hasAttribute("compact") });
    }
  }

  /**
   * Opens Chrome's side panel settings in a new tab.
   */
  openSidePanelSettings() {
    chrome.tabs.create({
      url: "chrome://settings/appearance#:~:text=side%20panel",
    });
  }

  /**
   * Shows the filter tabs popover, creating it if necessary.
   * @param {Event} event
   */
  showFilterTabsPopover({ currentTarget }) {
    if (this.filterTabsPopover) {
      this.filterTabsPopover.showPopover();
      return;
    }
    this.filterTabsPopover = new FilterTabsElement();
    currentTarget.appendChild(this.filterTabsPopover);
  }

  /**
   * Shows the sort tabs popover, creating it if necessary.
   * @param {Event} event
   */
  showSortTabsPopover({ currentTarget }) {
    if (this.sortTabsPopover) {
      this.sortTabsPopover.showPopover();
      return;
    }
    this.sortTabsPopover = new SortTabsElement();
    currentTarget.appendChild(this.sortTabsPopover);
  }

  /**
   * Shows the import/export dialog popover, creating it if necessary.
   * @param {Event} event
   */
  async showImportExportPopover({ currentTarget }) {
    if (this.importExportDialog) {
      this.importExportDialog.showPopover();
      return;
    }
    const { ImportExportDialog } = await import("./import-export-dialog-G6WE4YFT.js");
    this.importExportDialog = new ImportExportDialog();
    currentTarget.appendChild(this.importExportDialog);
  }

  /**
   * Renders the More menu popup with all available actions.
   * @returns {any}
   */
  render() {
    return c`
      <more-menu-popup id="more-menu" style="right:2px" popover>
        <li @click=${createTabAfterCurrent}>
          <vt-icon ico="plus"></vt-icon> <span>${i18n("create_tab")}</span> <kbd>Alt+N</kbd>
        </li>
        <li class="sort-tabs-btn" @click=${this.showSortTabsPopover.bind(this)}>
          <vt-icon ico="sort"></vt-icon> <span>${i18n("sort")}</span>
        </li>
        <li @click=${showTabGroupRuleDialog}>
          <vt-icon ico="group"></vt-icon> <span>${i18n("auto_group_rules")}</span><kbd>Ctrl+Alt+R</kbd>
        </li>
        <li @click=${groupAllUngroupedTabs}>
          <vt-icon ico="group-tabs"></vt-icon> <span>${i18n("auto_group_all_ungroup_tabs")}</span>
        </li>
        <li @click=${removeDuplicateTabs}>
          <vt-icon ico="duplicate"></vt-icon> <span>${i18n("remove_duplicate_tabs")}</span><kbd>Alt+D</kbd>
        </li>
        <li class="filter-tabs-btn" @click=${this.showFilterTabsPopover.bind(this)}>
          <vt-icon ico="filter"></vt-icon> <span>${i18n("filter")}</span>
        </li>
        <li @click=${openTabDiscardManager}>
          <vt-icon ico="suspend"></vt-icon> <span>${i18n("suspend_manager")}</span><kbd>Ctrl+Alt+S</kbd>
        </li>
        <li @click=${suspendOtherTabs}>
          <vt-icon ico="sleep"></vt-icon> <span>${i18n("suspend_other_tabs")}</span> <kbd>Ctrl+Shift+H</kbd>
        </li>
        <li @click=${openOptionsPage}>
          <vt-icon ico="settings"></vt-icon> <span>${i18n("settings")}</span><kbd>Ctrl+Alt+O</kbd>
        </li>
        <li @click=${this.toggleCompactMode.bind(this)}>
          <vt-icon ico="compact"></vt-icon> <span>${i18n("compact_mode")}</span><kbd>Ctrl+Alt+C</kbd>
        </li>
        <li @click=${this.openSidePanelSettings.bind(this)}>
          <vt-icon ico="panel-position"></vt-icon> <span>${i18n("panel_position")}</span>
        </li>
        <li @click=${this.showImportExportPopover.bind(this)}>
          <vt-icon ico="export"></vt-icon> <span>${i18n("import_export_tab")}</span><kbd>Ctrl+Shift+E</kbd>
        </li>
      </more-menu-popup>
      <button popovertarget="more-menu"><vt-icon ico="menu"></vt-icon></button>
    `;
  }

  /**
   * Lifecycle: called when element is added to the DOM.
   */
  connectedCallback() {
    this.replaceChildren(this.render());
  }
}

customElements.define("more-menu", MoreMenuElement);
/**
 * Constructs the good suffix table for the Boyer-Moore string search algorithm.
 * This table is used to determine how far the search window can be shifted when a mismatch occurs,
 * based on the matched suffix of the pattern (needle).
 *
 * @param {string} pattern - The pattern string for which to build the good suffix table.
 * @returns {number[]} An array of shift amounts for each suffix position in the pattern.
 */
function buildGoodSuffixTable(pattern) {
  /**
   * The resulting table of shift values for each suffix position.
   * Each entry represents how far the pattern can be shifted when a mismatch occurs at that position.
   * @type {number[]}
   */
  const goodSuffixShifts = [];
  const patternLength = pattern.length;

  // Iterate from the end of the pattern towards the start
  for (let suffixStart = patternLength - 1; suffixStart > 0; suffixStart--) {
    // The suffix to match (from current position to end)
    const suffix = pattern.slice(suffixStart);
    // The prefix to search for matching substrings
    const prefix = pattern.slice(0, suffixStart);
    const suffixLength = suffix.length;
    let shift = -1;

    // Search for the rightmost occurrence of the suffix in the prefix
    for (let j = prefix.length; j >= suffixLength; j--) {
      const window = prefix.slice(j - suffixLength, j);
      if (window === suffix) {
        // Found a matching substring; calculate the shift
        shift = patternLength - j;
        break;
      }
    }
    // If no match is found, stop building the table
    if (shift === -1) break;
    goodSuffixShifts.push(shift);
  }
  return goodSuffixShifts;
}
/**
 * Builds a bad character table for the Boyer-Moore string search algorithm.
 * This table maps each character in the pattern to its rightmost position.
 * @param {string} pattern - The search pattern.
 * @returns {Object.<string, number>} Character position mapping.
 */
function buildBadCharacterTable(pattern) {
  const charTable = {};
  const patternLength = pattern.length;
  for (let i = 0; i < patternLength; i++) {
    charTable[pattern[i]] = i;
  }
  return charTable;
}

/**
 * Boyer-Moore string search algorithm implementation.
 * Provides efficient string searching with both bad character and good suffix heuristics.
 */
class BoyerMooreSearcher {
  /**
   * @param {string} needle - The pattern to search for.
   */
  constructor(needle) {
    /** @type {string} The search pattern */
    this.needle = needle;
    /** @type {Object.<string, number>} Bad character table for quick character skips */
    this.badCharTable = buildBadCharacterTable(needle);
    /** @type {number[]} Good suffix table for pattern-based skips */
    this.goodSuffixTable = buildGoodSuffixTable(needle);
    /** @type {number} Length of the search pattern */
    this.count = needle.length;
  }

  /**
   * Gets the shift amount based on good suffix heuristic.
   * @param {number} mismatchIndex - Index where mismatch occurred.
   * @returns {number} Number of positions to shift.
   */
  getGoodShift(mismatchIndex) {
    let shift = this.goodSuffixTable[mismatchIndex];
    // Fallback to last entry or pattern length if no specific shift found
    shift ??= this.goodSuffixTable[this.goodSuffixTable.length - 1];
    shift ??= this.count;
    return shift;
  }

  /**
   * Searches for all occurrences of the pattern in the given text.
   * @param {string} text - The text to search in.
   * @returns {number[]} Array of starting positions where pattern was found.
   */
  searchAll(text) {
    const matches = [];
    let textIndex = 0;
    const maxIndex = text.length - this.count;

    while (textIndex <= maxIndex) {
      let patternIndex = this.count - 1;

      // Compare pattern from right to left
      while (
        this.needle[patternIndex] === text[textIndex + patternIndex] &&
        (patternIndex-- === 0 && matches.push(textIndex), patternIndex !== -1)
      );

      // Calculate shift using bad character heuristic
      const badCharShift = patternIndex - (this.badCharTable[text[textIndex + patternIndex]] ?? -1);
      let totalShift = badCharShift;

      // Use good suffix heuristic if we had a partial match
      if (patternIndex !== this.count - 1) {
        const goodSuffixShift = this.getGoodShift(patternIndex);
        totalShift = Math.max(badCharShift, goodSuffixShift);
      }

      textIndex += totalShift;
    }

    return matches;
  }
}
/**
 * SearchTabsElement - Custom element for searching and highlighting tabs.
 * Provides real-time search functionality with text highlighting using Boyer-Moore algorithm.
 * @element search-tabs
 */
class SearchTabsElement extends HTMLElement {
  /** @type {HTMLInputElement} The search input field */
  inputField;

  /** @type {Highlight} CSS Highlight API instance for text highlighting */
  highlighter;

  /** @type {HTMLElement} Reference to the tab container */
  tabContainer;

  constructor() {
    super();
    // Initialize CSS Highlight API for search result highlighting
    this.highlighter = new Highlight();
    CSS.highlights.set("search-highlight", this.highlighter);
  }

  /**
   * Highlights matching text ranges in the given text node.
   * @param {number[]} matchPositions - Array of match start positions.
   * @param {Text} textNode - The text node to highlight.
   */
  highlightMatchRange(matchPositions, textNode) {
    const searchLength = this.inputField.value.length;
    for (const position of matchPositions) {
      if (position + searchLength > textNode.length) continue;
      const range = new Range();
      range.setStart(textNode, position);
      range.setEnd(textNode, position + searchLength);
      this.highlighter.add(range);
    }
  }

  /**
   * Searches through all tabs and highlights matches in title and URL.
   * @param {BoyerMooreSearcher} searcher - The search algorithm instance.
   */
  searchTab(searcher) {
    this.highlighter.clear();
    for (const tabElement of this.tabContainer.shadowRoot.children) {
      const titleMatches = searcher.searchAll(tabElement.tab.title.toLowerCase());
      const urlMatches = searcher.searchAll(tabElement.tab.url.slice(8).toLowerCase());

      if (titleMatches.length === 0 && urlMatches.length === 0) {
        tabElement.hidden = true;
      } else {
        if (tabElement.hidden) {
          tabElement.hidden = false;
        }
        // Navigate to the text nodes for highlighting
        const textContainer = tabElement.lastElementChild.previousElementSibling.lastElementChild.firstElementChild;

        if (titleMatches.length > 0) {
          this.highlightMatchRange(titleMatches, textContainer.firstChild);
        }
        if (urlMatches.length > 0) {
          this.highlightMatchRange(urlMatches, textContainer.nextElementSibling.firstChild);
        }
      }
    }
  }

  /**
   * Handles search input events and triggers search or reset.
   * @param {{target: HTMLInputElement}} event - The input event.
   */
  searchQuery({ target }) {
    const query = target.value.toLowerCase();
    if (query) {
      const searcher = new BoyerMooreSearcher(query);
      this.searchTab(searcher);
    } else {
      this.reset();
    }
  }

  /**
   * Resets the search state, clearing highlights and showing all tabs.
   */
  reset() {
    this.highlighter.clear();
    for (const tabElement of this.tabContainer.shadowRoot.children) {
      if (tabElement.hidden) {
        tabElement.hidden = false;
      }
    }
  }

  /**
   * Focuses the search input field.
   */
  showSearchBox() {
    this.inputField.focus();
  }

  /**
   * Renders the search component HTML.
   * @returns {DocumentFragment} The rendered search interface.
   */
  render() {
    return templateLiteral`<search>
				<input
					type="search"
					placeholder="🔎 ${i18n("search_tabs")}"
					ref=${(element) => (this.inputField = element)}
					@input=${this.searchQuery.bind(this)} />
			</search>
			<vt-icon ico="search" @click=${this.showSearchBox.bind(this)}></vt-icon>`;
  }

  /**
   * Lifecycle: called when element is added to DOM.
   */
  connectedCallback() {
    this.style.position = "relative";
    this.replaceChildren(this.render());
    this.tabContainer = querySelector("tab-container");
  }
}
customElements.define("search-tabs", SearchTabsElement);
var E = class extends HTMLElement {
  constructor() {
    super();
  }
  createWorkspace() {
    let t = new A();
    return (
      $on(t, "create", async ({ detail: e }) => {
        let i = { id: Math.random().toString(36).slice(2), name: e },
          o = (await f("workspaces")).workspaces ?? [];
        o.push(i),
          m({ workspaces: o }),
          this.workspaces.splice(0, 0, i),
          this.switchWorkspace(i.id),
          this.firstElementChild.hidePopover(),
          ($(".workspace", this).textContent = i.name),
          ($(`input[value="${i.id}"]`, this).checked = !0);
      }),
      this.firstElementChild.appendChild(t)
    );
  }
  async switchWorkspace(t) {
    let e = await r({ windowId: -2 }),
      i = new Map((await D({ windowId: -2 })).map((a) => [a.id, a.title]));
    setStore({
      [this.atvWorkspaceId]: e.map((a) => ({
        url: a.url,
        groupTitle: i[a.groupId],
      })),
    }),
      (this.atvWorkspaceId = t),
      m({ atvWorkspaceId: this.atvWorkspaceId });
    let o = (await getStore(t))[t] ?? [];
    if (o.length === 0) return g({ index: 0, active: !1 });
    e.length > o.length && (await u(e.slice(o.length).map((a) => a.id))),
      toast("Restoring...");
    let n = {},
      p = $("tab-container");
    async function h(a) {
      let { id: l } = await chrome.tabs.discard(a),
        T = p.shadowRoot.getElementById(String(a));
      (T.id = l), (T.tab.id = l);
    }
    try {
      for (let a = 0; a < o.length; a++) {
        let l = o[a],
          { id: T } = e[a]
            ? await chrome.tabs.update(e[a].id, { url: l.url })
            : await g({ url: l.url, active: !1 });
        if (l.groupTitle)
          if (n[l.groupTitle])
            await chrome.tabs.group({ tabIds: T, groupId: n[l.groupTitle] });
          else {
            let R = await chrome.tabs.group({ tabIds: T });
            await chrome.tabGroups.update(R, {
              title: l.groupTitle,
              color: "cyan",
            }),
              (n[l.groupTitle] = R);
          }
        await new Promise((R) => setTimeout(R, 1e3)), await h(T);
      }
      notify("Workspace switched");
    } catch (a) {
      console.error(a), document.body.appendChild(new K(a));
    }
  }
  onWorkspaceChange(t) {
    let e = t.target.value;
    this.switchWorkspace(e),
      ($(".workspace", this).textContent =
        t.target.nextElementSibling.textContent);
  }
  onWorkspaceClick({ target: t }) {
    if (t.closest("vt-icon")) {
      let e = t.closest("li"),
        i = new A(e.textContent),
        o = async ({ detail: n }) => {
          e.firstElementChild.textContent = n;
          let p = $("input", e).value,
            { workspaces: h } = await f("workspaces"),
            a = h.findIndex((l) => l.id === p);
          (h[a].title = n), m({ workspaces: h });
        };
      return $on(i, "update", o), this.firstElementChild.appendChild(i);
    }
  }
  render(t) {
    let e = (i) => c`<li>
				<label>
					<input type="radio" name="workspace" value="${i.id}" hidden />
					<span>${i.name.slice(0, 12)}</span>
				</label>
				<vt-icon ico="edit"></vt-icon>
			</li>`;
    return c`<workspaces-popup
				id="workspaces"
				@change=${this.onWorkspaceChange.bind(this)}
				@click=${this.onWorkspaceClick.bind(this)}
				popover>
				${Z(this.workspaces, e)}
				<li @click=${this.createWorkspace.bind(this)}>
					<vt-icon ico="plus"></vt-icon><span>Create workspace</span>
				</li>
			</workspaces-popup>
			<div style="padding-top: 0.2em;">
				<span class="workspace">${t.slice(0, 10)}</span>
				<button popovertarget="workspaces">
					<vt-icon ico="chev-down" title="switch workspace" toggle></vt-icon>
				</button>
			</div>`;
  }
  async connectedCallback() {
    let {
      workspaceOn: t,
      workspaces: e,
      atvWorkspaceId: i,
    } = await f(["workspaceOn", "workspaces", "atvWorkspaceId"]);
    if (!t) return;
    (this.workspaces = W(e ?? [])),
      this.workspaces.length === 0 &&
        this.workspaces.push({ id: "workspace", name: "Workspace" }),
      (this.atvWorkspaceId = i || this.workspaces[0].id);
    let o = e?.find((n) => n.id === i)?.name ?? i18n("workspace");
    this.replaceChildren(this.render(o)),
      ($(`input[value="${this.atvWorkspaceId}"]`, this).checked = !0),
      $on(
        this.firstElementChild,
        "toggle",
        (n) =>
          n.newState === "closed" &&
          ($('vt-icon[ico="chev-down"]', this).checked = !1),
      );
  }
};
customElements.define("workspace-tabs", E);
var A = class extends HTMLDialogElement {
  constructor(t) {
    super(), (this.workspace = t);
  }
  updateWorkspace() {
    fireEvent(
      this,
      this.workspace ? "update" : "create",
      $("input", this).value,
    ),
      this.remove();
  }
  async showEmojiPicker({ target: t }) {
    if (this.emojiPicker) return this.emojiPicker.showPopover();
    let { EmojiPicker: e } = await import("./emoji-picker-GN6LRT62.js");
    (this.emojiPicker = new e()), t.before(this.emojiPicker);
  }
  render() {
    return c`<h2>${this.workspace ? "Update" : "Create"} ${i18n("workspace")}</h2>
			<label>
				<span>${i18n("name")}</span> <br />
				<input type="text" />
				<span class="emoji-btn" title="Pick emoji" @click=${this.showEmojiPicker.bind(this)}> 😃 </span>
			</label>
			<div>
				<button class="outline-btn" @click=${this.remove.bind(this)}>${i18n("cancel")}</button>
				<button @click=${this.updateWorkspace.bind(this)}>
					${this.workspace ? i18n("update") : i18n("create")}
				</button>
			</div>`;
  }
  connectedCallback() {
    (this.id = "update-workspace-dialog"),
      this.replaceChildren(this.render()),
      this.showModal(),
      $on(this, "toggle", (t) => t.newState === "closed" && this.remove());
  }
};
customElements.define("update-workspace-dialog", A, { extends: "dialog" });
import Ht from "./window-bar-QFG34U26.css" with { type: "css" };
var Et,
  G = (s) => (Et.firstChild.data = s),
  ot = class extends HTMLElement {
    constructor() {
      super(),
        this.attachShadow({ mode: "open" }),
        (this.shadowRoot.adoptedStyleSheets = [Ht]);
    }
    render() {
      return [
        c`<div class="tab-coin">
			<div class="coin-ring">
				<div class="tab-num" ref=${(e) => (Et = e)}>0</div>
			</div>
		</div>`,
        new ut(),
        new E(),
        new S(),
        new _(),
      ];
    }
    async connectedCallback() {
      this.shadowRoot.replaceChildren(...this.render());
    }
  };
customElements.define("window-bar", ot);
var w;
function nt(s) {
  (w ??= $("tab-container")),
    s.theme && s.theme !== "none"
      ? w.style.setProperty("--tab-bgc", `url(${dt + s.theme + ".svg"})`)
      : w.style.removeProperty("--tab-bgc"),
    s.fontFamily && w.style.setProperty("--font-family", s.fontFamily),
    s.fontSize && w.style.setProperty("--font-size", s.fontSize),
    s.textColor && w.style.setProperty("--txt-clr", s.textColor);
}
chrome.runtime.onMessage.addListener((s, t, e) => {
  s.msg === "config" && nt(s);
});
async function Ut(s) {
  w ??= $("tab-container");
  let t = await r({ windowId });
  function e(i) {
    chrome.tabs
      .discard(i)
      .then((o) => {
        let n = w.shadowRoot.getElementById(String(i));
        (n.id = o.id), (n.tab.id = o.id);
      })
      .catch((o) => console.error(o));
  }
  t.forEach((i) => {
    if (!i.discarded) {
      if (!i.lastAccessed) return e(i.id);
      Date.now() > i.lastAccessed + s && e(i.id);
    }
  });
}
chrome.storage.sync
  .get(["autoSuspensionOn", "autoSuspensionTime"])
  .then((s) => {
    let { autoSuspensionOn: t, autoSuspensionTime: e } = s;
    if (!t) return;
    let i = (e ?? 30) * 60 * 1e3;
    t && setInterval(() => Ut, 5 * 60 * 60 * 1e3, i);
  });
import Ot from "./marked-action-BXXJLEWR.css" with { type: "css" };
var d = new Set();
function At(s) {
  let t = s.target.checked,
    e = +s.target.parentElement.id;
  t
    ? (d.add(e),
      d.size === 1 ? document.body.appendChild(new V()) : M.setCount())
    : (d.delete(e), d.size === 0 ? M.remove() : M.setCount());
}
var M,
  V = class extends HTMLElement {
    countElem;
    constructor() {
      super(),
        (this.status = "active"),
        this.attachShadow({ mode: "open" }),
        (this.shadowRoot.adoptedStyleSheets = [Ot]),
        (this.marktabs = null),
        (M = this);
    }
    async closeTabs() {
      try {
        await u([...d]), this.clear(), notify(d.size + " tabs closed");
      } catch (t) {
        console.error(t);
      }
    }
    async suspendTabs() {
      let t = [];
      for (let e of d) t.push(chrome.tabs.discard(e));
      try {
        await Promise.allSettled(t), notify(d.size + " tabs suspended");
      } catch (e) {
        console.error(e);
      }
      this.resetMarked();
    }
    resetMarked() {
      fireEvent(document.body, "unselectall"), this.clear();
    }
    clear() {
      d.clear(), this.remove(), (M = null);
    }
    async addInGroup() {
      let { TabgroupSelect: t } = await import("./tabgroup-select-KETNIX4M.js");
      this.shadowRoot.appendChild(new t([...d]));
    }
    async moveToOtherWindow() {
      let { WindowSelect: t } = await import("./window-select-CYH65ETX.js");
      this.shadowRoot.appendChild(new t([...d]));
    }
    setCount() {
      this.countElem.textContent = d.size;
    }
    render() {
      return c`<div class="marked-card">
			<div><output ref=${(t) => (this.countElem = t)}>1</output> ${i18n("selected")}</div>
			<div class="marked-action-wrapper">
				<vt-icon ico="group" title="${i18n("add_tabs_to_group")}" @click=${this.addInGroup.bind(this)}></vt-icon>
				<vt-icon
					ico="windows"
					title="${i18n("move_tabs_to_window")}"
					@click=${this.moveToOtherWindow.bind(this)}></vt-icon>
				<vt-icon ico="suspend" title="${i18n("suspend_selected_tabs")}" @click=${this.suspendTabs}></vt-icon>
				<vt-icon ico="delete" title="${i18n("close_selected_tabs")}" @click=${this.closeTabs}></vt-icon>
			</div>
			<vt-icon
				ico="close"
				title="${i18n("unselect_all")}"
				style="margin-left: auto;"
				@click=${this.resetMarked.bind(this)}></vt-icon>
		</div>`;
    }
    connectedCallback() {
      this.shadowRoot.replaceChildren(this.render());
    }
  };
customElements.define("marked-action", V);
var v = class extends HTMLElement {
  constructor(t) {
    super(),
      (this._internals = this.attachInternals()),
      (this.tab = t),
      (this.tabIndex = 0),
      (this.id = this.tab.id.toString()),
      (this.pinned = this.tab.pinned),
      (this.suspend = this.tab.discarded);
  }
  set active(t) {
    t
      ? this._internals.states.add("active")
      : this._internals.states.delete("active");
  }
  set pinned(t) {
    t
      ? this._internals.states.add("pinned")
      : this._internals.states.delete("pinned");
  }
  set collapsed(t) {
    t
      ? this._internals.states.add("collapsed")
      : this._internals.states.delete("collapsed"),
      this.nextElementSibling?.tab.groupId === this.tab.groupId &&
        (this.nextElementSibling.collapsed = t);
  }
  set suspend(t) {
    t
      ? this._internals.states.add("suspend")
      : this._internals.states.delete("suspend");
  }
  closeTab(t) {
    t.stopImmediatePropagation(),
      chrome.tabs.remove(this.tab.id).catch((e) => console.error(e));
  }
  suspendTab() {
    chrome.tabs
      .discard(this.tab.id)
      .then((t) => {
        (this.id = t.id.toString()),
          (this.tab.id = t.id),
          toast("Tab suspended");
      })
      .catch((t) => console.error(t));
  }
  activateTab() {
    chrome.windows
      .update(this.tab.windowId, { focused: !0 })
      .catch((t) => console.error(t)),
      chrome.tabs
        .update(this.tab.id, { active: !0 })
        .catch((t) => console.error(t));
  }
  render() {
    let t = this.tab;
    return c`<input
				type="checkbox"
				class="mark-tab"
				tabindex="-1"
				title="${i18n("select_tab")}"
				@change=${At} />
			<tab-info @click=${this.activateTab.bind(this)}>
				<img src=${() => t.favIconUrl} loading="lazy" @error=${Bt} />
				<div>
					<div class="tab-title">${() => t.title.replaceAll("<", "<")}</div>
					<div class="tab-url">${() => t.url.slice(8)}</div>
				</div>
			</tab-info>
			<div class="action-btn-box">
				<vt-icon
					ico="sleep"
					class="sleep-btn"
					title="${i18n("suspend_tab")}"
					@click=${this.suspendTab.bind(this)}></vt-icon>
				<vt-icon
					ico="close"
					class="close-btn"
					title="${i18n("close_tab")}"
					@click=${this.closeTab.bind(this)}></vt-icon>
			</div> `;
  }
  connectedCallback() {
    this.setAttribute("draggable", "true"), this.replaceChildren(this.render());
  }
};
customElements.define("tab-item", v);
function Bt() {
  this.src = "/assets/web.svg";
}
function at(s) {
  let t = s.target.closest("tab-item")?.tab;
  t &&
    ((L ??= document.body.appendChild(new tt())),
    L.setTab(t.id, t.index),
    (L.style.left = `min(36%, ${s.pageX}px)`),
    (L.style.top = s.pageY + "px"),
    s.preventDefault());
}
var L,
  tt = class extends HTMLElement {
    constructor() {
      super(), (L = this);
    }
    setTab(t, e, i) {
      (this.tabId = t),
        (this.tabIndex = e),
        (this.windowId = i),
        setTimeout(() => this.showPopover(), 100);
    }
    menuActions = {
      add_previous_tab: () => chrome.tabs.create({ index: this.tabIndex }),
      add_next_tab: () => chrome.tabs.create({ index: this.tabIndex + 1 }),
      add_tab_to_group: () => F(this.tabId),
      move_tab_to_window: () => z(this.tabId),
      reload: () => chrome.tabs.reload(this.tabId),
      duplicate: () => chrome.tabs.duplicate(this.tabId),
      pin: () =>
        chrome.tabs.update({ pinned: !0 }).catch((t) => console.error(t)),
      mute: () =>
        chrome.tabs.update({ muted: !0 }).catch((t) => console.error(t)),
      suspend: () =>
        chrome.tabs.discard(this.tabId).catch((t) => console.error(t)),
      copy_url: async () => {
        let t = (await P(this.tabId)).url;
        navigator.clipboard
          .writeText(t)
          .then(() => toast("Copied"))
          .catch((e) => console.error(e));
      },
      close_above_tabs: async () => {
        let t = (await r({ windowId: this.windowId }))
          .slice(0, this.tabIndex)
          .map((e) => e.id);
        chrome.tabs.remove(t).catch((e) => console.error(e));
      },
      close_below_tabs: async () => {
        let t = (await r({ windowId: this.windowId }))
          .slice(this.tabIndex)
          .map((e) => e.id);
        chrome.tabs.remove(t).catch((e) => console.error(e));
      },
      close_other_tabs: j,
    };
    async onMenuItemClick({ target: t }) {
      let e = t.closest("li")?.id;
      e && (await this.menuActions[e](), this.hidePopover());
    }
    render() {
      let t = (i) => c`<li id=${i.id}>
			<vt-icon ico="${i.icon}" title="edit group"></vt-icon>
			<span>${i.title}</span>
			<kbd>${i.keyShortcut}</kbd>
		</li>`;
      return [
        {
          id: "add_previous_tab",
          icon: "plus",
          title: i18n("new_tab_above"),
          keyShortcut: "Ctrl+Alt+N",
        },
        {
          id: "add_next_tab",
          icon: "tab-plus",
          title: i18n("new_tab_below"),
          keyShortcut: "Alt+N",
        },
        {
          id: "add_tab_to_group",
          icon: "group",
          title: i18n("add_tab_to_group"),
          keyShortcut: "Alt+G",
        },
        {
          id: "move_tab_to_window",
          icon: "move",
          title: i18n("move_tab_to_window"),
          keyShortcut: "Alt+M",
        },
        {
          id: "reload",
          icon: "reload",
          title: i18n("reload_tab"),
          keyShortcut: "Ctrl+R",
        },
        {
          id: "duplicate",
          icon: "duplicate",
          title: i18n("duplicate_tab"),
          keyShortcut: "Alt+D",
        },
        { id: "pin", icon: "pin", title: i18n("pin_tab"), keyShortcut: "" },
        { id: "mute", icon: "mute", title: i18n("mute_tab"), keyShortcut: "" },
        {
          id: "suspend",
          icon: "sleep",
          title: i18n("suspend_tab"),
          keyShortcut: "Alt+H",
        },
        {
          id: "copy_url",
          icon: "copy",
          title: i18n("copy_url"),
          keyShortcut: "Ctrl+C",
        },
        {
          id: "close_above_tabs",
          icon: "close-multiple",
          title: i18n("close_above_tabs"),
          keyShortcut: "Ctrl+Shift+Q",
        },
        {
          id: "close_below_tabs",
          icon: "close-multiple",
          title: i18n("close_below_tabs"),
          keyShortcut: "Ctrl+Alt+W",
        },
        {
          id: "close_other_tabs",
          icon: "close-multiple",
          title: i18n("close_other_tabs"),
          keyShortcut: "Ctrl+Alt+Q",
        },
      ].map(t);
    }
    connectedCallback() {
      this.replaceChildren(...this.render()),
        this.setAttribute("popover", ""),
        $on(this, "click", this.onMenuItemClick);
    }
  };
customElements.define("tabaction-menu", tt);
var jt = "/assets/spin.svg",
  Gt = (s) =>
    class extends s {
      groupMap = new Map();
      activeTab;
      onCreateTab(t) {
        this.tabs.splice(t.index, 0, t), G(this.tabs.length);
      }
      updateTab(t, e, i) {
        if (e.groupId)
          e.groupId === -1
            ? this.removeGroupFromTab(this.shadowRoot.children[i.index])
            : this.updateGroupInfoOnTab(i.index, e.groupId);
        else if (e.status) {
          if (
            ((this.tabs[i.index].favIconUrl =
              e.status === "loading" ? jt : i.favIconUrl),
            e.discarded !== void 0)
          )
            return setTimeout(
              () =>
                (this.shadowRoot.getElementById(String(t)).suspend =
                  e.discarded),
              100,
            );
        } else if (e.pinned !== void 0)
          return (this.shadowRoot.getElementById(String(t)).pinned = e.pinned);
        this.tabs[i.index] && Object.assign(this.tabs[i.index], e);
      }
      onmoveTab(t, e) {
        let i = this.tabs[e.fromIndex];
        this.tabs.splice(e.fromIndex, 1);
        for (let o = e.fromIndex; o < this.tabs.length; o++)
          --this.tabs[o].index;
        (i.index = e.toIndex), this.tabs.splice(e.toIndex, 0, i);
        for (let o = e.toIndex + 1; o < this.tabs.length; ++o)
          ++this.tabs[o].index;
        i.groupId === -1 || this.updateGroupInfoOnTab(i.index, i.groupId);
      }
      onDetachTab(t) {
        this.tabs.splice(t.oldPosition, 1);
      }
      async onAttachTab(t, e) {
        let i = await P(t);
        this.tabs.splice(e.newPosition, 0, i);
      }
      onRemoveTab(t) {
        let e = this.tabs.findIndex((i) => i.id === t);
        e !== -1 && (this.tabs.splice(e, 1), G(this.tabs.length));
      }
      activateTab(t) {
        this.activeTab && (this.activeTab.active = !1),
          (this.activeTab = this.shadowRoot.getElementById(String(t))),
          (this.activeTab.active = !0);
      }
      removeGroupFromTab(t) {
        if (t.firstElementChild.tagName === "GROUP-BAR") {
          let e = t.tab.groupId,
            i = this.groupMap.get(e);
          i &&
            t.nextElementSibling?.tab.groupId === e &&
            t.nextElementSibling.prepend(i),
            t.style.removeProperty("--grp-clr");
        }
      }
      async updateGroupInfoOnTab(t, e) {
        let i = this.shadowRoot.children[t],
          o = await chrome.tabGroups.get(e).catch((n) => console.error(n));
        if (o)
          if ((b(i, o.color), this.groupMap.has(e))) {
            let n = this.groupMap.get(e);
            if (n instanceof v) return this.insertTabGroup(o);
            n.parentElement.tab.index > i.tab.index && i.prepend(n);
          } else
            this.shadowRoot.hasChildNodes() &&
              (this.groupMap.set(e, i), this.insertTabGroup(o));
      }
      onUpdateGroup(t) {
        let e = this.groupMap.get(t.id);
        e &&
          ((e.title = t.title),
          (e.color = t.color),
          (e.collapsed = t.collapsed));
      }
      onRemoveGroup(t) {
        let e = this.groupMap.get(t.id);
        e && (this.groupMap.delete(t.id), e.remove());
      }
      setListener() {
        chrome.tabs.onCreated.addListener(
          (t) => t.windowId === this.windowId && this.onCreateTab(t),
        ),
          chrome.tabs.onUpdated.addListener(
            (t, e, i) =>
              i.windowId === this.windowId && this.updateTab(t, e, i),
          ),
          chrome.tabs.onActivated.addListener(
            (t) => t.windowId === this.windowId && this.activateTab(t.tabId),
          ),
          chrome.tabs.onRemoved.addListener(
            (t, { windowId: e }) => e === this.windowId && this.onRemoveTab(t),
          ),
          chrome.tabs.onMoved.addListener(
            (t, e) => e.windowId === this.windowId && this.onmoveTab(t, e),
          ),
          chrome.tabs.onDetached.addListener(
            (t, e) => e.oldWindowId === this.windowId && this.onDetachTab(e),
          ),
          chrome.tabs.onAttached.addListener(
            (t, e) => e.newWindowId === this.windowId && this.onAttachTab(t, e),
          ),
          chrome.tabGroups.onUpdated.addListener(
            (t) => t.windowId === this.windowId && this.onUpdateGroup(t),
          ),
          chrome.tabGroups.onRemoved.addListener(
            (t) => t.windowId === this.windowId && this.onRemoveGroup(t),
          );
      }
    };
var y = class extends HTMLElement {
  constructor(t) {
    super(),
      (this.tabGroup = t),
      (this._internals = this.attachInternals()),
      (this.collapsed = this.tabGroup.collapsed);
  }
  set title(t) {
    this.tabGroup.title !== t &&
      ((this.tabGroup.title = t), (this.children[1].textContent = t));
  }
  set color(t) {
    this.tabGroup.color !== t &&
      ((this.tabGroup.color = t), this.updateGroupTabsClr(t));
  }
  get collapsed() {
    return this._internals.states.has("collapsed");
  }
  set collapsed(t) {
    t
      ? this._internals.states.add("collapsed")
      : this._internals.states.delete("collapsed"),
      this.tabGroup.collapsed !== t &&
        ((this.tabGroup.collapsed = t),
        (this.parentElement.collapsed = this.collapsed));
  }
  toggleGroup() {
    (this.collapsed = !this.tabGroup.collapsed),
      (this.tabGroup.collapsed = this.collapsed),
      chrome.tabGroups.update(this.tabGroup.id, { collapsed: this.collapsed });
  }
  updateGroup({ detail: t }) {
    (this.title = t.title),
      (this.color = t.color),
      chrome.tabGroups.update(this.tabGroup.id, {
        title: t.title,
        color: t.color,
      });
  }
  async unGroupTab() {
    let t = (await r({ groupId: this.tabGroup.id })).map(({ id: e }) => e);
    await chrome.tabs.ungroup(t).catch((e) => console.error(e));
  }
  async removeTabs() {
    let t = (await r({ groupId: this.tabGroup.id })).map(({ id: e }) => e);
    await chrome.tabs.remove(t).catch((e) => console.error(e));
  }
  async suspendGroup() {
    try {
      let t = [],
        e = await r({ groupId: this.tabGroup.id });
      for (let i of e) t.push(chrome.tabs.discard(i.id));
      await Promise.all(t), notify("Tabgroup suspended");
    } catch (t) {
      console.error(t);
    }
  }
  moveTabGroup() {
    this.appendChild(new mt(null, this.tabGroup.id));
  }
  editTabGroup() {
    this.appendChild(new pt(this.tabGroup));
  }
  render() {
    return c`<vt-icon
				ico="chev-down"
				class="collapse-btn"
				title="${i18n("collapse/uncollapse_group")}"
				@click=${this.toggleGroup.bind(this)}></vt-icon>
			<span>${this.tabGroup.title}</span>
			<div class="group-action">
				<vt-icon ico="edit" title="${i18n("edit_group")}" @click=${this.editTabGroup.bind(this)}></vt-icon>
				<vt-icon
					ico="windows"
					title="${i18n("move_group_to_window")}"
					@click=${this.moveTabGroup.bind(this)}></vt-icon>
				<vt-icon
					ico="suspend"
					title="${i18n("suspend_tabgroup")}"
					@click=${this.suspendGroup.bind(this)}></vt-icon>
				<vt-icon ico="ungroup" title="${i18n("ungroup_tabs")}" @click=${this.unGroupTab.bind(this)}></vt-icon>
				<vt-icon ico="delete" title="${i18n("delete_group")}" @click=${this.removeTabs.bind(this)}></vt-icon>
			</div>`;
  }
  connectedCallback() {
    this.setAttribute("tabindex", "0"),
      this.replaceChildren(this.render()),
      b(this.parentElement, this.tabGroup.color),
      setTimeout($t, 100, this.tabGroup);
  }
  updateGroupTabsClr(t) {
    b(this.parentElement, t);
    let e = this.parentElement.nextElementSibling;
    for (; e?.tab.groupId === this.tabGroup.id; )
      b(e, t), (e = e.nextElementSibling);
  }
};
customElements.define("group-bar", y);
import Nt from "./tab-container-HC4YG376.css" with { type: "css" };
import Ft from "./tab-item-2YWBHU6I.css" with { type: "css" };
var rt,
  ct = class extends Gt(HTMLElement) {
    constructor() {
      super(),
        this.attachShadow({ mode: "open" }),
        (this.shadowRoot.adoptedStyleSheets = [Nt, Ft]),
        f("theme").then((t) => nt(t));
    }
    render(t) {
      return c`${Z(this.tabs, (e) => {
        let i = new v(e);
        return (
          e.groupId !== -1 &&
            (this.groupMap.has(e.groupId) || this.groupMap.set(e.groupId, i),
            b(i, t.get(e.groupId))),
          i
        );
      })}`;
    }
    async connectedCallback() {
      let { compactMode: t } = await f("compactMode");
      t && this.setAttribute("compact", ""),
        (this.windowId = (await chrome.windows.getCurrent()).id),
        this.setWindowTabs(this.windowId),
        this.setListener(),
        $on(this.shadowRoot, "contextmenu", at),
        $on(
          this.shadowRoot,
          "dragstart",
          ({ target: e }) => (rt = e.closest("tab-item").id),
        ),
        $on(this.shadowRoot, "dragover", (e) => e.preventDefault()),
        $on(this.shadowRoot, "drop", this.setDropped.bind(this)),
        $on(
          this.shadowRoot,
          "auxclick",
          (e) => e.which === 2 && u(+e.target.closest("tab-item").id),
        ),
        $on(document.body, "windowswitch", ({ detail: e }) =>
          this.setWindowTabs(e),
        ),
        $on(document.body, "unselectall", () => {
          for (let e of this.shadowRoot.children)
            e.firstElementChild.checked = !1;
        }),
        addEventListener("paste", Ct);
    }
    async setWindowTabs(t) {
      (this.windowId = t), this.groupMap.clear();
      try {
        let e = await r({ windowId: t }),
          i = await D({ windowId: t }),
          o = new Map(i.map((n) => [n.id, n.color]));
        (this.tabs = W(e)),
          this.shadowRoot.replaceChildren(this.render(o)),
          this.groupMap.size === 0 || i.forEach(this.insertTabGroup, this),
          r({ active: !0, windowId: t }).then((n) => this.activateTab(n[0].id)),
          G(e.length);
      } catch (e) {
        console.error(e), document.body.appendChild(new K(e));
      }
    }
    groupMap = new Map();
    insertTabGroup(t) {
      if (!this.groupMap.has(t.id)) return;
      let e = new y(t);
      this.groupMap.get(t.id).prepend(e),
        this.groupMap.set(t.id, e),
        $on(e, "remove", ({ detail: i }) => this.onRemoveGroup(i));
    }
    async setDropped({ target: t }) {
      let e = t.closest("tab-item").tab.index;
      await chrome.tabs
        .move(+rt, { index: e })
        .catch((i) => notify("cannot move tab", "error"))
        .finally(() => (rt = null));
    }
  };
customElements.define("tab-container", ct);
import zt from "./base-ERUOT5RD.css" with { type: "css" };
import Qt from "./snackbar-CLPBJTKZ.css" with { type: "css" };
document.adoptedStyleSheets.push(zt, Qt);
globalThis.windowId = -2;
