/**
 * @fileoverview
 * Window management UI components for a Chrome extension.
 * Provides:
 * - <window-list>: A custom element for displaying and switching between Chrome windows.
 * - <update-window-dialog>: A dialog for renaming a window.
 * - <window-select>: A custom element for moving tabs or tab groups to a window.
 * All elements use modern JS idioms, are fully documented, and preserve all original behaviors.
 */

import {
  moveTabGroups,
  getSessionStorageItem,
  setSessionStorageItem,
  makeReactive,
  html,
  bindArrayToFragment,
} from "./chunk-BHCSV6LT.js";

/**
 * Fires a custom event on the given element.
 * @param {Element} element
 * @param {string} eventName
 * @param {*} detail
 */
function fireEvent(element, eventName, detail) {
  element.dispatchEvent(new CustomEvent(eventName, { detail, bubbles: true }));
}

/**
 * Shorthand for document.querySelector or scoped querySelector.
 * @param {string} selector
 * @param {Element|Document} [scope=document]
 * @returns {Element|null}
 */
function $(selector, scope = document) {
  return (scope || document).querySelector(selector);
}

/**
 * Shorthand for adding an event listener.
 * @param {Element} element
 * @param {string} event
 * @param {Function} handler
 */
function $on(element, event, handler) {
  element.addEventListener(event, handler);
}

/**
 * <window-list>: Displays a list of Chrome windows and allows switching/renaming.
 */
class WindowListElement extends HTMLElement {
  constructor() {
    super();
    /** @type {number[]} */
    this.windows = [];
    /** @type {number} */
    this.crtWindowId = null;
  }

  /**
   * Handles switching the active window.
   * @param {Event} event
   */
  onWindowSwitch(event) {
    globalThis.windowId = +event.target.value;
    fireEvent(document.body, "windowswitch", globalThis.windowId);
  }

  /**
   * Handles clicking the edit icon to rename a window.
   * @param {{target: Element}} param0
   */
  onWindowClick({ target }) {
    if (!target.closest("vt-icon")) return;
    const listItem = target.closest("li");
    const dialog = new UpdateWindowDialog(listItem.textContent);
    $on(dialog, "update", ({ detail: newName }) => {
      listItem.firstElementChild.textContent = newName;
      const windowId = +listItem.id;
      setSessionStorageItem({ [windowId]: newName });
      if (this.crtWindowId === windowId) {
        $(".win-title").textContent = newName.slice(0, 10);
      }
    });
    this.firstElementChild.appendChild(dialog);
  }

  /**
   * Adds a window to the list.
   * @param {{id: number}} windowObject
   */
  addWindow({ id }) {
    this.windows.push(id);
  }

  /**
   * Removes a window from the list.
   * @param {number} windowId
   */
  removeWindow(windowId) {
    const windowIndex = this.windows.findIndex((window) => window.id === windowId);
    this.windows.splice(windowIndex, 1);
  }

  /**
   * Renders the window list UI.
   * @param {Object<number, string>} windowNames
   * @returns {DocumentFragment}
   */
  render(windowNames) {
    const renderWindowItem = (windowId) => html`<li id="${windowId}">
      <label>
        <input type="radio" name="window" value="${windowId}" hidden />
        <span>${windowNames[windowId]?.slice(0, 12) ?? windowId}</span>
      </label>
      <vt-icon ico="edit"></vt-icon>
    </li>`;
    return html`<window-list-popup
      id="window-list"
      style="left:2em"
      @change=${this.onWindowSwitch.bind(this)}
      @click=${this.onWindowClick.bind(this)}
      popover>
      ${bindArrayToFragment(this.windows, renderWindowItem)}
    </window-list-popup>
    <div style="padding-top: 0.2em;">
      <span class="win-title">${windowNames[this.crtWindowId]?.slice(0, 11) ?? this.crtWindowId}</span>
      <button popovertarget="window-list">
        <vt-icon ico="chev-down" title="Switch window" toggle></vt-icon>
      </button>
    </div>`;
  }

  /**
   * Initializes the element and sets up listeners.
   */
  async connectedCallback() {
    const windowIds = (await chrome.windows.getAll({ windowTypes: ["normal"] })).map((window) => window.id);
    this.windows = makeReactive(windowIds);
    this.crtWindowId = (await chrome.windows.getCurrent()).id;
    const windowNames = await getSessionStorageItem(null);
    windowNames[this.crtWindowId] ??= "This window";
    this.replaceChildren(this.render(windowNames));
    this.setListener();
    $(`input[value="${this.crtWindowId}"]`, this).checked = true;
    $on(
      this.firstElementChild,
      "toggle",
      (event) => event.newState === "closed" && ($('vt-icon[ico="chev-down"]', this).checked = false),
    );
  }

  /**
   * Sets up Chrome window event listeners.
   */
  setListener() {
    chrome.windows.onCreated.addListener(this.addWindow.bind(this), {
      windowTypes: ["normal"],
    });
    chrome.windows.onRemoved.addListener(this.removeWindow.bind(this), {
      windowTypes: ["normal"],
    });
  }
}
customElements.define("window-list", WindowListElement);

/**
 * <update-window-dialog>: Dialog for renaming a window.
 */
class UpdateWindowDialog extends HTMLDialogElement {
  /**
   * @param {string} winName
   */
  constructor(winName) {
    super();
    this.winName = winName;
    /** @type {HTMLElement} */
    this.emojiPicker = null;
  }

  /**
   * Fires update event and closes dialog.
   */
  updateWindow() {
    fireEvent(this, "update", $("input", this).value);
    this.remove();
  }

  /**
   * Shows emoji picker for the window name.
   * @param {{target: Element}} param0
   */
  async showEmojiPicker({ target }) {
    if (this.emojiPicker) return this.emojiPicker.showPopover();
    const { EmojiPicker } = await import("./emoji-picker-6IWPZZFB.js");
    this.emojiPicker = new EmojiPicker();
    target.before(this.emojiPicker);
  }

  /**
   * Renders the dialog UI.
   * @returns {DocumentFragment}
   */
  render() {
    return html`<h2>${i18n("update_window_name")}</h2>
      <label>
        <span>${i18n("name")}</span> <br />
        <input type="text" />
        <span class="emoji-btn" title="Pick emoji" @click=${this.showEmojiPicker.bind(this)}> 😃 </span>
      </label>
      <div>
        <button class="outline-btn" @click=${this.remove.bind(this)}>${i18n("cancel")}</button>
        <button @click=${this.updateWindow.bind(this)}>${i18n("update")}</button>
      </div>`;
  }

  /**
   * Initializes the dialog and sets up listeners.
   */
  connectedCallback() {
    this.id = "update-win-dialog";
    this.replaceChildren(this.render());
    this.showModal();
    $on(this, "toggle", (e) => e.newState === "closed" && this.remove());
  }
}
customElements.define("update-window-dialog", UpdateWindowDialog, { extends: "dialog" });

/**
 * <window-select>: UI for moving tabs or tab groups to a window.
 */
class WindowSelectElement extends HTMLElement {
  /**
   * @param {number[]} tabIds
   * @param {number} tabGroupId
   */
  constructor(tabIds, tabGroupId) {
    super();
    this.tabIds = tabIds;
    this.tabGroupId = tabGroupId;
    /** @type {number[]} */
    this.windowIds = [];
  }

  /**
   * Moves tabs or tab group to the selected window.
   * @param {{target: HTMLInputElement}} param0
   */
  async moveToWindow({ target }) {
    try {
      const moveOptions = {
        index: -1,
        windowId: +target.value || (await chrome.windows.create({ state: "maximized" })).id,
      };
      if (this.tabIds) {
        await chrome.tabs.move(this.tabIds, moveOptions);
      } else if (this.tabGroupId) {
        await moveTabGroups(this.tabGroupId, moveOptions);
      }
      this.remove();
      $("marked-action")?.remove();
    } catch {
      console.error();
    }
  }

  /**
   * Handles clicking the edit icon to rename a window or create a new window.
   * @param {{target: Element}} param0
   */
  onWindowClick({ target }) {
    const listItem = target.closest("li");
    if (listItem.id === "newWindow") return this.moveToWindow({ target });
    if (!target.closest("vt-icon")) return;
    const dialog = new UpdateWindowDialog(listItem.textContent);
    $on(dialog, "update", ({ detail: newName }) => (listItem.firstElementChild.textContent = newName));
    this.firstElementChild.appendChild(dialog);
  }

  /**
   * Renders the window selection UI.
   * @returns {DocumentFragment}
   */
  render() {
    const renderWindowItem = (windowId) => html`<li>
      <label><input type="radio" name="window" value="${windowId}" hidden /><span>${windowId}</span></label>
      <vt-icon ico="edit" title="Rename window"></vt-icon>
    </li>`;
    return html`<li>
        <select name="position" disabled>
          <option value="-1">window's end</option>
          <option value="5">window's middle</option>
          <option value="0">window's start</option>
        </select>
      </li>
      ${this.windowIds.map(renderWindowItem)}
      <li id="newWindow">
        <cite>${i18n("new_window")}</cite>
        <tv-icon ico="plus"></tv-icon>
      </li>`;
  }

  /**
   * Initializes the element and sets up listeners.
   */
  async connectedCallback() {
    this.id = "window-select";
    this.style.left = "4em";
    this.setAttribute("popover", "");
    this.windowIds = (await chrome.windows.getAll({ windowTypes: ["normal"] })).map(({ id }) => id);
    this.replaceChildren(this.render());
    this.showPopover();
    $on(this, "change", this.moveToWindow);
    $on(this, "click", this.onWindowClick);
    $on(this, "toggle", (e) => e.newState === "closed" && this.remove());
  }
}
customElements.define("window-select", WindowSelectElement);

export {
  WindowListElement as WindowList,
  WindowSelectElement as WindowSelect,
};
