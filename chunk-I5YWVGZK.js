//! Deobfuscated and documented version of chunk-I5YWVGZK.js

/**
 * Chrome API utility bindings for tabs and tab groups.
 * Provides convenient, bound versions of common Chrome extension APIs.
 */
const queryTabs = chrome.tabs.query.bind(chrome.tabs);
const getTab = chrome.tabs.get.bind(chrome.tabs);
const createTab = chrome.tabs.create.bind(chrome.tabs);
const removeTab = chrome.tabs.remove.bind(chrome.tabs);
const queryTabGroups = chrome.tabGroups.query.bind(chrome.tabGroups);
const moveTabGroup = chrome.tabGroups.move.bind(chrome.tabGroups);

const getLocalStorageItem = localStorage.getItem.bind(localStorage);
const setLocalStorageItem = localStorage.setItem.bind(localStorage);

const getSyncStorage = chrome.storage.sync.get.bind(chrome.storage.sync);
const setSyncStorage = chrome.storage.sync.set.bind(chrome.storage.sync);

const getSessionStorage = chrome.storage.session.get.bind(chrome.storage.session);
const setSessionStorage = chrome.storage.session.set.bind(chrome.storage.session);

const getSessionItem = sessionStorage.getItem.bind(sessionStorage);
const setSessionItem = sessionStorage.setItem.bind(sessionStorage);

/**
 * List of supported tab group colors.
 * @type {string[]}
 */
const TAB_GROUP_COLORS = [
  "blue", "red", "yellow", "green", "cyan", "purple", "yellow", "pink", "orange", "grey"
];

/**
 * RGB color values for tab group colors.
 * @type {Object<string, string>}
 */
const TAB_GROUP_COLOR_RGB = {
  blue: "0, 0, 255",
  red: "255, 0, 0",
  yellow: "255, 251, 0",
  green: "0, 128, 0",
  cyan: "0, 255, 255",
  purple: "128, 0, 128",
  pink: "255, 192, 203",
  orange: "255, 165, 0",
  grey: "128, 128, 128",
};

/**
 * Base URL for extension themes.
 * @type {string}
 */
const THEME_BASE_URL = "https://crxextstatic.blob.core.windows.net/themes/";

/**
 * Internal state for DOM reactivity and binding.
 * @typedef {Object} ReactState
 * @property {Node|null} node
 * @property {string|null} attr
 * @property {Function|null} valFn
 * @property {Function|null} fragFn
 * @property {string|null} style
 * @property {Comment|null} cmt
 */
const reactState = {
  node: null,
  attr: null,
  valFn: null,
  fragFn: null,
  style: null,
  cmt: null,
};

/**
 * Set of attribute names that are directly bound to element properties.
 * @type {Set<string>}
 */
const DIRECT_BIND_ATTRS = new Set(["value", "checked", "open"]);

/**
 * Class representing a reactive DOM node binding.
 */
class ReactiveNodeBinding {
  constructor() {
    this.node = reactState.node;
    this.attr = reactState.attr;
    this.valFn = reactState.valFn;
    this.style = reactState.style;
  }
}

/**
 * Class representing a reactive comment node binding (for fragments).
 */
class ReactiveCommentBinding {
  constructor() {
    this.fragFn = reactState.fragFn;
    this.cmtNode = reactState.cmt;
  }
}

/**
 * Utility functions for updating DOM nodes and fragments reactively.
 */
const ReactDOMUtils = {
  /**
   * Replace a child node with a fragment or node.
   * @param {Node} commentNode
   * @param {Node|Node[]} replacement
   */
  replaceChildFragment(commentNode, replacement) {
    if (Array.isArray(replacement)) {
      commentNode.parentNode.replaceChildren(commentNode, ...replacement);
    } else {
      commentNode.parentNode.replaceChild(replacement, commentNode.nextSibling);
    }
  },

  /**
   * Set a sibling node or fragment after a comment node.
   * @param {Object} binding
   */
  setSibling({ cmtNode, fragFn }) {
    const fragment = fragFn();
    if (fragment instanceof Promise) {
      fragment.then(resolved => ReactDOMUtils.replaceChildFragment(cmtNode, resolved));
    } else {
      ReactDOMUtils.replaceChildFragment(
        cmtNode,
        fragment || new Comment(String(Math.random()).slice(9))
      );
    }
  },

  /**
   * Set the value of a DOM node or attribute.
   * @param {Object} binding
   */
  setValue(binding) {
    const node = binding.node;
    if (binding.attr) {
      if (binding.style) {
        node.style.setProperty(binding.style, binding.valFn());
      } else if (node[binding.attr] !== undefined && node instanceof HTMLElement) {
        node[binding.attr] = binding.valFn();
      } else {
        node.setAttribute(binding.attr, binding.valFn());
      }
    } else {
      node.textContent = binding.valFn();
    }
  },

  /**
   * Update a binding (either a comment node or a value).
   * @param {Object} binding
   */
  update(binding) {
    binding.cmtNode ? ReactDOMUtils.setSibling(binding) : ReactDOMUtils.setValue(binding);
  }
};

/**
 * Utility functions for binding DOM elements to data.
 */
const ReactBindingUtils = {
  /**
   * Set up a two-way binding between an element and an object property.
   * @param {HTMLElement} element
   * @param {string} attr
   * @param {Object} obj
   * @param {string} key
   */
  setBinding(element, attr, obj, key) {
    const valueKey = element.valueAsNumber ? "valueAsNumber" : attr;
    element.addEventListener(element.bindEvent, function () {
      obj[key] = this[valueKey];
    });
  },

  /**
   * Insert a reactive data binding into a map.
   * @param {Object} obj
   * @param {string} key
   * @param {Object} binding
   */
  insertReactData(obj, key, binding) {
    if (obj._reactMap.has(key)) {
      const existing = obj._reactMap.get(key);
      Array.isArray(existing)
        ? existing.push(binding)
        : obj._reactMap.set(key, [existing, binding]);
    } else {
      obj._reactMap.set(key, binding);
    }
  },

  /**
   * Create a new reactive data binding.
   * @returns {ReactiveNodeBinding|ReactiveCommentBinding}
   */
  makeReactData(...args) {
    let binding;
    if (reactState.node) {
      binding = new ReactiveNodeBinding();
      if (
        DIRECT_BIND_ATTRS.has(reactState.attr) &&
        reactState.node.bindEvent
      ) {
        ReactBindingUtils.setBinding(reactState.node, reactState.attr, ...args);
      }
    } else if (reactState.cmt) {
      binding = new ReactiveCommentBinding();
    }
    return binding;
  }
};

/**
 * Recursively wraps an object or array in a Proxy to enable reactivity.
 * @param {Object|Array} obj
 * @returns {Proxy}
 */
function makeReactive(obj) {
  if (obj._reactMap || typeof obj !== "object") return obj;
  if (Array.isArray(obj)) return makeReactiveArray(obj);

  Object.defineProperty(obj, "_reactMap", { value: new Map(), enumerable: false });
  for (const key in obj) {
    if (obj[key] && typeof obj[key] === "object") {
      obj[key] = Array.isArray(obj[key])
        ? makeReactiveArray(obj[key])
        : makeReactive(obj[key]);
    }
  }
  return new Proxy(obj, {
    get(target, prop, receiver) {
      const binding = ReactBindingUtils.makeReactData(target, prop);
      if (binding) ReactBindingUtils.insertReactData(target, prop, binding);
      return Reflect.get(target, prop, receiver);
    },
    set(target, prop, value) {
      if (typeof value !== "object" && target[prop] === value) return true;
      Reflect.set(target, prop, value);
      const binding = target._reactMap.get(prop);
      if (binding) {
        Array.isArray(binding)
          ? binding.forEach(ReactDOMUtils.update)
          : ReactDOMUtils.update(binding);
      }
      return true;
    },
    deleteProperty(target, prop) {
      const binding = target._reactMap.get(prop);
      if (!binding) return true;
      const removeComment = b =>
        b.cmtNode?.nextSibling.replaceWith(
          new Comment(String(Math.random()).slice(9))
        );
      Array.isArray(binding) ? binding.forEach(removeComment) : removeComment(binding);
      delete target[prop];
      return true;
    }
  });
}

/**
 * Wraps an array in a Proxy to enable reactivity for array operations.
 * @param {Array} arr
 * @returns {Proxy}
 */
function makeReactiveArray(arr) {
  if (!Array.isArray(arr)) {
    console.error("input object must be array");
    return;
  }
  arr = arr.map(item => makeReactive(item));
  let binding;
  const wrapArray = items => items.map(item => makeReactive(item));
  const mapArray = (items, fn) => items.map(fn);

  function insertAfter(index, items, context) {
    const nodes = mapArray(items, context.yieldfn);
    if (!nodes) return;
    const isArray = Array.isArray(nodes);
    if (index === 0) {
      isArray ? context.comment.after(...nodes) : context.comment.after(nodes);
      return;
    }
    const prev = context.comment.parentNode.children[index - 1];
    isArray ? prev.after(...nodes) : prev.after(nodes);
  }

  function updateItem(index, item, context) {
    if (context.updateItem) {
      const node = context.comment.parentNode.children[index];
      if (node) context.updateItem(node, item);
    } else {
      context.comment.parentNode.children[index].replaceWith(context.yieldfn(item));
    }
  }

  function removeItems(index, count, context) {
    if (!(count <= 0)) {
      for (let i = 0; i < count; i++) {
        context.comment.parentNode.children[index]?.remove();
      }
    }
  }

  function updateFragment(index, count, items) {
    const context = arr._react[0];
    let temp;
    if (items) {
      if (count > 0) {
        temp ??= items.splice(0, count);
        const len = temp.length;
        if (len !== 0) {
          for (let i = 0; i < len; i++) updateItem(index + i, temp[i], context);
        }
        removeItems(index + len, count - len, context);
      }
      items && insertAfter(index, items, context);
    } else if (count > 0) {
      removeItems(index, count, context);
    }
  }

  const arrayMethods = {
    push: () =>
      function (...items) {
        items = wrapArray(items);
        arr.push.call(arr, ...items);
        if (arr._react) {
          for (const context of arr._react) {
            const nodes = mapArray(items, context.yieldfn);
            const parent = context.comment.parentElement;
            Array.isArray(nodes) ? parent.append(...nodes) : parent.append(nodes);
          }
          binding && ReactDOMUtils.update(binding);
        }
      },
    pop: () =>
      function () {
        arr.pop.apply(arr, arguments);
        arr._react[0].comment.parentNode.lastElementChild.remove();
      },
    splice: () =>
      function (start, deleteCount, ...items) {
        if (!arr._react) return arr.splice.apply(arr, arguments);
        items = wrapArray(items);
        const removed = arr.splice.call(arr, start, deleteCount, ...items);
        updateFragment(start, deleteCount, items);
        binding && items.length - deleteCount !== 0 && ReactDOMUtils.update(binding);
        return removed;
      },
    shift: () =>
      function () {
        arr.shift.apply(arr, arguments);
        updateFragment(0, 1);
      },
    unshift: () =>
      function (...items) {
        arr.unshift.apply(arr, arguments);
        updateFragment(0, 0, items);
      },
    filter: () =>
      function () {
        if (!arr._react) return arr.filter.apply(arr, arguments);
        const predicate = arguments[0];
        const context = arr._react[0];
        const children = context.comment.parentNode.children;
        let len = arr.length;
        while (len--) {
          if (!predicate(arr[len])) {
            children[len]?.remove();
            arr.splice(len, 1);
          }
        }
        return this;
      },
    reverse: () =>
      function () {
        arr.reverse.apply(arr, arguments);
        const context = arr._react[0];
        context.comment.parentNode.append(
          ...Array.from(context.comment.parentNode.childNodes).reverse()
        );
      },
    sort: () =>
      function () {
        arr.sort.apply(arr, arguments);
        updateFragment(0, arr.length, [...arr]);
      },
    length: () => (
      reactState.cmt ? (binding = new ReactiveCommentBinding()) : reactState.node && (binding = new ReactiveNodeBinding()), arr.length
    ),
  };

  return new Proxy(arr, {
    get(target, prop, receiver) {
      return arrayMethods[prop] ? arrayMethods[prop]() : Reflect.get(target, prop, receiver);
    },
    deleteProperty(target, prop) {
      arr._react[0].comment.parentNode.children[Number(prop)].remove();
      delete target[prop];
      return true;
    },
    defineProperty(target, prop, descriptor) {
      Object.defineProperty(target, prop, descriptor);
      return Reflect.get(target, prop);
    }
  });
}

/**
 * Regular expression for extracting attribute names from template strings.
 * @type {RegExp}
 */
const ATTRIBUTE_NAME_REGEX = /\s\.(.*)=$/;

/**
 * Internal maps for managing template and reactivity state.
 */
const functionMap = new Map();
const reactFunctionMap = new Map();
const extractedFunctionMap = new Map();
const objectHolderMap = new Map();

/**
 * Template string extraction and interpolation utilities.
 */
const TemplateUtils = {
  strings: null,
  stringArr: null,

  /**
   * Extracts and interpolates values into a template string.
   * Handles functions, arrays, promises, and objects for reactivity.
   * @param {TemplateStringsArray} strings
   * @param  {...any} values
   * @returns {string}
   */
  extract(strings, ...values) {
    this.strings = strings;
    this.stringArr = [strings[0]];
    const n = values.length;
    for (let i = 0; i < n; i++) {
      const value = values[i];
      const typeName = value?.constructor?.name;
      if (TemplateUtils.instances[typeName]) {
        TemplateUtils.instances[typeName](value, i);
      } else if (typeof value === "object") {
        this.setObjHolder(value, i);
      } else {
        this.stringArr.push(value, strings[i + 1]);
      }
    }
    const result = "".concat(...this.stringArr);
    this.strings = null;
    this.stringArr = null;
    return result;
  },

  /**
   * Type-specific extractors for template interpolation.
   */
  instances: {
    Function: (value, i) =>
      value.name ? TemplateUtils.extractFunc(value, i) : TemplateUtils.extractReactfn(value, i),
    AsyncFunction: (value, i) => TemplateUtils.extractFunc(value, i),
    DocumentFragment: (value, i) => functionMap.set(TemplateUtils.setComment(i), value),
    Array: (value, i) => TemplateUtils.extractArray(value, i),
    Promise: (value, i) => TemplateUtils.extractPromise(value, i),
    Object: (value, i) => TemplateUtils.setObjHolder(value, i),
  },

  /**
   * Extracts a named function for later use.
   * @param {Function} fn
   * @param {number} i
   */
  extractFunc(fn, i) {
    const key = String(Math.random()).slice(9);
    extractedFunctionMap.set(key, fn);
    this.stringArr.push(key, this.strings[i + 1]);
  },

  /**
   * Extracts a function for reactivity in templates.
   * @param {Function} fn
   * @param {number} i
   */
  extractReactfn(fn, i) {
    if (this.strings[i].endsWith("=") || this.strings[i].endsWith(":")) {
      this.setReactHolder(fn, i);
    } else if (this.strings[i].endsWith(">")) {
      functionMap.set(this.setComment(i), fn);
    } else if (this.strings[i].trim().endsWith(">")) {
      functionMap.set(this.setComment(i, "?^"), fn);
    } else {
      functionMap.set(this.setComment(i, this.strings[i] || "?^"), fn);
    }
  },

  /**
   * Extracts an array for template interpolation.
   * @param {Array} arr
   * @param {number} i
   */
  extractArray(arr, i) {
    if (this.strings[i].endsWith("=")) {
      this.setObjHolder(arr, i);
    } else if (arr[0] instanceof Node) {
      functionMap.set(this.setComment(i), arr);
    } else {
      this.stringArr.push(...arr, this.strings[i + 1]);
    }
  },

  /**
   * Stores an object for later attribute binding.
   * @param {Object} obj
   * @param {number} i
   */
  setObjHolder(obj, i) {
    const attrName = this.strings[i].match(ATTRIBUTE_NAME_REGEX)?.[1];
    if (!attrName) {
      console.warn("object only pass at attribute");
      return;
    }
    objectHolderMap.set(attrName, obj);
    this.stringArr.push(attrName, this.strings[i + 1]);
  },

  /**
   * Inserts a comment node marker for template interpolation.
   * @param {number} i
   * @param {string} [prefix="?"]
   * @returns {string}
   */
  setComment(i, prefix = "?") {
    const key = ` ${prefix}${String(Math.random()).slice(9)} `;
    this.stringArr.push(`<!--${key}--> `, this.strings[i + 1]);
    return key;
  },

  /**
   * Handles promises in template interpolation.
   * @param {Promise} promise
   * @param {number} i
   */
  extractPromise(promise, i) {
    const key = this.setComment(i, "");
    promise.then(result => ReactTemplateUtils.setPromiseFragment(key, result));
  },

  /**
   * Stores a function for later reactivity binding.
   * @param {Function} fn
   * @param {number} i
   */
  setReactHolder(fn, i) {
    const key = "%" + String(Math.random()).slice(9);
    reactFunctionMap.set(key, fn);
    this.stringArr.push(key, this.strings[i + 1]);
  }
};

/**
 * Utilities for managing template fragments and reactivity.
 */
const ReactTemplateUtils = {
  /**
   * Replace a comment node with a fragment or node.
   * @param {Comment} commentNode
   * @param {Node|Node[]} replacement
   */
  replaceChildFragment(commentNode, replacement) {
    if (Array.isArray(replacement)) {
      commentNode.parentNode.replaceChildren(commentNode, ...replacement);
    } else {
      commentNode.parentNode.replaceChild(replacement, commentNode.nextSibling);
    }
  },

  /**
   * Insert a fragment or node after a comment node for reactivity.
   * @param {Comment} commentNode
   * @param {Function} fragFn
   */
  insertReactChildFragment(commentNode, fragFn) {
    reactState.cmt = commentNode;
    reactState.fragFn = fragFn;
    const fragment = reactState.fragFn();
    if (fragment instanceof Promise) {
      fragment.then(resolved => this.replaceChildFragment(commentNode, resolved));
    } else {
      commentNode.after(fragment || new Comment(String(Math.random()).slice(9)));
    }
    reactState.fragFn = reactState.cmt = null;
  },

  /**
   * Insert a text node after a comment node for reactivity.
   * @param {Comment} commentNode
   * @param {Function} valFn
   */
  insertReactTextContent(commentNode, valFn) {
    reactState.node = new Text();
    reactState.valFn = valFn;
    reactState.node.textContent = reactState.valFn();
    commentNode.parentNode.replaceChild(reactState.node, commentNode);
    reactState.node = reactState.valFn = null;
  },

  /**
   * Replace all comment nodes in a fragment with their corresponding content.
   * @param {DocumentFragment} fragment
   */
  setChildFragment(fragment) {
    const iterator = document.createNodeIterator(
      fragment,
      NodeFilter.SHOW_COMMENT,
      node =>
        node.nodeValue?.startsWith(" ?")
          ? NodeFilter.FILTER_ACCEPT
          : NodeFilter.FILTER_REJECT
    );
    let commentNode;
    while ((commentNode = iterator.nextNode())) {
      const value = functionMap.get(commentNode.nodeValue);
      if (value) {
        if (typeof value === "function") {
          if (commentNode.nodeValue.startsWith(" ?^")) {
            this.insertReactChildFragment(commentNode, value);
          } else {
            this.insertReactTextContent(commentNode, value);
          }
        } else if (Array.isArray(value)) {
          commentNode.after(...value);
        } else {
          commentNode.parentNode.replaceChild(value, commentNode);
        }
        functionMap.delete(commentNode.nodeValue);
      }
    }
  },

  /**
   * Replace a comment node with the result of a resolved promise.
   * @param {string} key
   * @param {Node|Node[]} result
   */
  setPromiseFragment(key, result) {
    const node = document
      .createNodeIterator(document.body, NodeFilter.SHOW_COMMENT, n =>
        n.nodeValue === key
          ? NodeFilter.FILTER_ACCEPT
          : NodeFilter.FILTER_REJECT
      )
      .nextNode();
    if (node) this.replaceChildFragment(node, result);
  }
};

/**
 * Attribute and event binding utilities for template rendering.
 */
const TemplateAttributeUtils = {
  /**
   * Mapping of tag names to their default mutation events.
   */
  mutatingNodes: {
    DETAILS: "toggle",
    DIALOG: "close",
    INPUT: "change",
    SELECT: "change",
    TEXTAREA: "change"
  },

  /**
   * Handles event attribute binding (e.g., @click).
   */
  "@": (element, attr, attrs) => {
    element.addEventListener(attr.name.slice(1), extractedFunctionMap.get(attr.value));
    attrs.removeNamedItem(attr.name);
    return true;
  },

  /**
   * Retrieves and applies a reactive attribute value.
   */
  retrieveAttr: (element, attr, attrs) => {
    reactState.node = element;
    reactState.attr = attr.name.slice(1);
    reactState.valFn = reactFunctionMap.get(attr.value);
    if (!reactState.valFn) {
      console.error(`${attr.name} value is not a function or not found`);
      return;
    }
    element[reactState.attr] = reactState.valFn();
    reactState.node = reactState.attr = reactState.valFn = null;
  },

  /**
   * Handles property binding (e.g., .value).
   */
  ".": (element, attr, attrs) => {
    if (TemplateAttributeUtils.mutatingNodes[element.tagName]) {
      element.bindEvent = TemplateAttributeUtils.mutatingNodes[element.tagName];
    }
    if (reactFunctionMap.has(attr.value)) {
      TemplateAttributeUtils.retrieveAttr(element, attr, attrs);
    } else {
      element[attr.name.slice(1)] = attr.value ?? objectHolderMap.get(attr.value) ?? "";
    }
    attrs.removeNamedItem(attr.name);
    return true;
  },

  /**
   * Handles boolean property binding (e.g., ?checked).
   */
  "?": (element, attr, attrs) => {
    if (TemplateAttributeUtils.mutatingNodes[element.tagName]) {
      element.bindEvent = TemplateAttributeUtils.mutatingNodes[element.tagName];
    }
    if (reactFunctionMap.has(attr.value)) {
      TemplateAttributeUtils.retrieveAttr(element, attr, attrs);
    } else {
      element[attr.name.slice(1)] = attr.value === "true";
    }
    attrs.removeNamedItem(attr.name);
    return true;
  },

  /**
   * Handles ref attribute binding.
   */
  ref: (element, attr, attrs) => {
    if (attr.name === "ref") {
      reactFunctionMap.get(attr.value)?.(element);
      attrs.removeNamedItem(attr.name);
      return true;
    }
  },

  /**
   * Handles style attribute binding with reactivity.
   */
  style: (element, attr) => {
    if (attr.name !== "style") return;
    reactState.node = element;
    reactState.attr = attr.name;
    const styles = attr.value.split(";");
    for (const style of styles) {
      const [prop, value] = style.split(":", 2);
      if (value?.startsWith("%")) {
        reactState.style = prop;
        reactState.valFn = reactFunctionMap.get(value);
        element.style.setProperty(prop, reactState.valFn());
      }
    }
    reactState.node = reactState.attr = reactState.style = reactState.valFn = null;
    return true;
  },

  /**
   * Handles generic attribute binding with reactivity.
   */
  "%": (element, attr) => {
    reactState.node = element;
    reactState.attr = attr.name;
    reactState.valFn = reactFunctionMap.get(attr.value);
    element.setAttribute(attr.name, reactState.valFn());
    reactState.node = reactState.attr = reactState.valFn = null;
  },

  /**
   * Parses and applies all attributes on a node.
   * @param {Element} element
   */
  parseNodeAttributes(element) {
    const attrs = element.attributes;
    let n = attrs.length;
    while (n--) {
      const attr = attrs[n];
      this[attr.name.at(0)]?.(element, attr, attrs) ||
        (attr.value.startsWith("%") && this["%"]?.(element, attr));
    }
  }
};

/**
 * Renders a template string with reactivity and returns a DocumentFragment.
 * @param {TemplateStringsArray} strings
 * @param  {...any} values
 * @returns {DocumentFragment}
 */
function renderTemplate(strings, ...values) {
  const html = TemplateUtils.extract(strings, ...values);
  const fragment = document.createRange().createContextualFragment(html);
  for (const element of fragment.querySelectorAll("*")) {
    if (element.hasAttributes()) TemplateAttributeUtils.parseNodeAttributes(element);
  }
  if (functionMap.size > 0) ReactTemplateUtils.setChildFragment(fragment);
  clearTemplateState();
  return fragment;
}

/**
 * Clears all internal template and reactivity maps.
 */
function clearTemplateState() {
  reactFunctionMap.clear();
  extractedFunctionMap.clear();
  objectHolderMap.clear();
}

/**
 * Binds an array to a template fragment for reactivity.
 * @param {Array} arr
 * @param {Function} yieldFn
 * @param {Function} [updateItem]
 * @returns {Array}
 */
function bindReactiveArray(arr, yieldFn, updateItem) {
  const nodes = arr.map(yieldFn);
  const comment = new Comment(` #${String(Math.random()).slice(9)} `);
  nodes.unshift(comment);
  if (arr._react === undefined) {
    Object.defineProperty(arr, "_react", {
      value: [{ comment, yieldfn: yieldFn, updateItem }]
    });
  } else {
    arr._react.push({ comment, yieldfn: yieldFn, updateItem });
  }
  return nodes;
}

// Export all utilities and bindings
export {
  queryTabs as a,
  getTab as b,
  createTab as c,
  removeTab as d,
  queryTabGroups as e,
  moveTabGroup as f,
  getSyncStorage as g,
  setSyncStorage as h,
  getSessionStorage as i,
  setSessionStorage as j,
  TAB_GROUP_COLORS as k,
  TAB_GROUP_COLOR_RGB as l,
  THEME_BASE_URL as m,
  makeReactive as n,
  renderTemplate as o,
  bindReactiveArray as p
};

//!test
