import "./chunk-EK7ODJWE.js";
// The CSS file is missing, but the import is preserved for integration.
import emojiPickerStyles from "./emoji-picker-KRZLCODK.css" with { type: "css" };

/**
 * EmojiPicker is a custom HTML element that displays a categorized emoji picker UI.
 * It fetches emoji data, renders categories, and allows users to insert emojis into an input.
 * 
 * @class EmojiPicker
 * @extends HTMLElement
 */
class EmojiPicker extends HTMLElement {
  /**
   * Stores the emoji data, keyed by category.
   * @type {Record<string, string[]>}
   */
  emojis = {};

  constructor() {
    super();
    // Attach shadow DOM and apply styles
    this.attachShadow({ mode: "open" });
    this.shadowRoot.adoptedStyleSheets = [emojiPickerStyles];
  }

  /**
   * Renders the static HTML structure for the emoji picker.
   * @returns {string} HTML string for the picker UI
   */
  render() {
    return `
      <header class="category-bar">
        <div data-ctg="animals">\u{1F436}</div>
        <div data-ctg="food_drinks">\u{1F355}</div>
        <div data-ctg="travels">\u{1F680}</div>
        <div data-ctg="objects">\u{1F389}</div>
        <div data-ctg="symbols">\u2764\uFE0F</div>
        <div data-ctg="flags">\u{1F3C1}</div>
      </header>
      <article class="emoji-container">
        <div class="emoji-category" id="animals"></div>
        <div class="emoji-category" id="food_drinks"></div>
        <div class="emoji-category" id="travels"></div>
        <div class="emoji-category" id="objects"></div>
        <div class="emoji-category" id="symbols"></div>
        <div class="emoji-category" id="flags"></div>
      </article>
    `;
  }

  /**
   * Populates the emoji container for a given category, if not already populated.
   * Scrolls the category into view.
   * @param {string} category - The emoji category to display
   */
  showCategoryEmoji(category) {
    const categoryElement = this.shadowRoot.lastElementChild.querySelector(`#${category}`);
    categoryElement.scrollIntoView();
    if (categoryElement.hasChildNodes()) return; // Already populated

    const emojiList = this.emojis[category];
    let count = emojiList.length;
    const fragment = new DocumentFragment();

    // Add each emoji as a span
    while (count--) {
      const emojiSpan = document.createElement("span");
      emojiSpan.textContent = emojiList[count];
      fragment.appendChild(emojiSpan);
    }
    categoryElement.appendChild(fragment);
  }

  /**
   * Fetches emoji data from a remote API and updates the store.
   * @returns {Promise<Record<string, string[]>>} The emoji data
   */
  async fetchEmoji() {
    const response = await fetch("https://api.npoint.io/ac2f15a998f82653d938", {
      mode: "cors",
    });
    const emojiData = response.ok && (await response.json());
    setStore({ emojis: emojiData });
    return emojiData;
  }

  /**
   * Lifecycle callback: runs when the element is added to the DOM.
   * Sets up the UI, fetches emoji data, and attaches event listeners.
   * @returns {Promise<void>}
   */
  async connectedCallback() {
    this.id = "emoji-picker";
    this.setAttribute("popover", "");
    this.shadowRoot.innerHTML = this.render();

    // Try to get emoji data from store, otherwise fetch
    const { emojis } = await getStore("emojis");
    this.emojis = emojis || (await this.fetchEmoji());

    // Show the default category
    this.showCategoryEmoji("animals");
    this.showPopover();

    /**
     * Handles emoji selection: inserts the emoji at the start of the previous input.
     * @param {PointerEvent} event
     */
    const handleEmojiClick = ({ target }) => {
      if (target.nodeName === "SPAN") {
        this.previousElementSibling.value = target.textContent + this.previousElementSibling.value;
      }
    };

    /**
     * Handles category selection: shows the selected emoji category.
     * @param {MouseEvent} event
     */
    const handleCategoryClick = ({ target }) => {
      if (target.nodeName === "DIV") {
        this.showCategoryEmoji(target.dataset.ctg);
      }
    };

    // Attach event listeners
    $on(this.shadowRoot.lastElementChild, "pointerdown", handleEmojiClick);
    $on(this.shadowRoot.firstElementChild, "click", handleCategoryClick);
  }
}

// Register the custom element
customElements.define("emoji-picker", EmojiPicker);
export { EmojiPicker };
