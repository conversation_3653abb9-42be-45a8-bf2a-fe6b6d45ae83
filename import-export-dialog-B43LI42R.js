import { c as createTab, i as getWindowTitles, o as html } from "./chunk-I5YWVGZK.js";
import "./chunk-EK7ODJWE.js";

/**
 * Utility class for exporting tab data in various formats.
 */
class TabExportUtility {
  /**
   * Stores window titles for HTML export.
   * @type {Record<number, string>}
   */
  winTitles = {};

  constructor() {}

  /**
   * Converts tab data to JSON string.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toJSON(windowData) {
    const simplifiedTabs = windowData.tabs.map(({ title, url, favIconUrl }) => ({
      title,
      url,
      favIconUrl,
    }));
    return JSON.stringify(simplifiedTabs);
  }

  /**
   * Converts tab data to CSV format.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toCSV(windowData) {
    const header = `url,title,favIconUrl
`;
    const rows = windowData.tabs
      .map(({ title, url, favIconUrl }) => `${url},${title},${favIconUrl}`)
      .join(`
`);
    return header + rows;
  }

  /**
   * Converts tab data to Markdown format.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string}
   */
  toMarkdown(windowData) {
    return windowData.tabs
      .map(({ title, url }) => `[${title}](${url})`)
      .join(`
`);
  }

  /**
   * Converts tab data to HTML format, including window titles.
   * @param {Array<{ id: number, tabs: Array<{title: string, url: string, favIconUrl: string}> }>} windows
   * @returns {string}
   */
  toHTML(windows) {
    const docStart = `<!DOCTYPE html>
		<html lang="en">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>Tabs at ${new Date().toLocaleString("default", { dateStyle: "full" })}</title>
		</head>
		<body>
		`;

    // Render a single tab as a list item
    const renderTab = (tab) => `<li style="padding-block: 0.2em">
            <a href="${tab.url}" target="_blank" rel="noopener noreferrer">${tab.title}</a>
        </li>`;

    // Render a window section with its tabs
    const renderWindow = (window) => `<details>
            <summary>${this.winTitles[window.id] ?? window.id}</summary>
            <ol>
                ${window.tabs.map(renderTab).join("")}
            </ol>
        </details>`;

    return `${docStart}<blockquote >
            <h1>${new Date().toLocaleString("default", { dateStyle: "full" })}</h1>
            ${windows.map(renderWindow).join(`
`)}
        </blockquote></body></html>`;
  }

  /**
   * Exports the current window's tabs in the specified format and triggers a download.
   * @param {"json"|"csv"|"markdown"|"html"} format
   * @returns {Promise<void>}
   */
  async export(format) {
    // Get the current window and its tabs
    const windowData = await chrome.windows.get(windowId, {
      windowTypes: ["normal"],
      populate: true,
    });
    // Get window titles for HTML export
    this.winTitles = await getWindowTitles(null);
    // Generate the export content
    const content = this[`to${format.charAt(0).toUpperCase() + format.slice(1)}`](windowData);
    this.downloadFile(content, format);
  }

  /**
   * Triggers a download of the given content as a file.
   * @param {string} content
   * @param {string} format
   */
  downloadFile(content, format) {
    const blob = new Blob([content], { type: "text/plain" });
    const anchor = document.createElement("a");
    anchor.setAttribute("href", URL.createObjectURL(blob));
    anchor.setAttribute("download", new Date().toISOString() + "." + format);
    anchor.click();
  }
}

/**
 * Custom element for the Import/Export dialog.
 * Handles importing URLs from files and exporting tabs.
 * @element import-export-dialog
 */
class ImportExportDialog extends HTMLElement {
  constructor() {
    super();
  }

  /**
   * Handles file upload for importing URLs.
   * @param {Event} event
   */
  onFileUpload(event) {
    const urlRegex = /https:\/\/[^\s,"')\n]+/g;
    const file = event.target.files[0];
    const reader = new FileReader();

    reader.onload = async (loadEvent) => {
      const text = loadEvent.target.result;
      if (typeof text !== "string") return;
      const matches = text.matchAll(urlRegex);
      for (const match of matches) {
        const url = match[0];
        if (url && URL.canParse(url)) {
          // Open the URL in a new tab in the current window
          const { id: tabId } = await createTab({ url, windowId });
          // Wait for the tab to load before discarding
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await chrome.tabs.discard(tabId);
        }
      }
    };
    reader.onerror = (error) => console.error(error);
    reader.readAsText(file);
  }

  /**
   * Initiates export of tabs in the selected format.
   */
  exportTabs() {
    const format = this.querySelector("select").value;
    new TabExportUtility().export(format);
  }

  /**
   * Scrolls to the selected tab section.
   * @param {{ target: HTMLSelectElement }} param0
   */
  switchTab({ target }) {
    this.querySelector("#" + target.value).scrollIntoView();
  }

  /**
   * Renders the dialog UI.
   * @returns {any}
   */
  render() {
    return html`<header @change=${this.switchTab.bind(this)}>
				<label>${i18n("import")} <input type="radio" name="import-export" value="import-tab" hidden /> </label>
				<label
					>${i18n("export")} <input type="radio" name="import-export" value="export-tab" hidden checked />
				</label>
			</header>
			<div>
				<section id="import-tab">
					<input type="file" accept="text/html,text/csv,application/json" @change=${this.onFileUpload} />
					<button>${i18n("import")}</button>
				</section>
				<section id="export-tab">
					<label>
						<span>${i18n("format")}</span>
						<select name="format">
							<option value="html">html</option>
							<option value="json">json</option>
							<option value="markdown">markdown</option>
							<option value="csv">csv</option>
							<option value="sql">SQL</option>
						</select>
					</label>
					<button @click=${this.exportTabs.bind(this)}>Export</button>
				</section>
			</div> `;
  }

  /**
   * Lifecycle: called when the element is added to the DOM.
   */
  connectedCallback() {
    this.style.top = "import-export";
    this.setAttribute("popover", "");
    this.replaceChildren(this.render());
    this.showPopover();
    this.querySelector("#export-tab").scrollIntoView();
  }
}

customElements.define("import-export-dialog", ImportExportDialog);
export { ImportExportDialog };
