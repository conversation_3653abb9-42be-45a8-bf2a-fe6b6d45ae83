import { c as createTabInWindow, i as getWindowTitles, o as html } from "./chunk-BHCSV6LT.js";
import "./chunk-EK7ODJWE.js";

/**
 * Utility class for exporting tab data in various formats.
 */
class TabExportUtility {
  /**
   * Stores window titles for HTML export.
   * @type {Record<number, string>}
   */
  winTitles = {};

  constructor() {}

  /**
   * Exports tab data as JSON.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string} JSON string of tab data.
   */
  toJSON(windowData) {
    const tabs = windowData.tabs.map(({ title, url, favIconUrl }) => ({
      title,
      url,
      favIconUrl,
    }));
    return JSON.stringify(tabs);
  }

  /**
   * Exports tab data as CSV.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string} CSV string of tab data.
   */
  toCSV(windowData) {
    const header = `url,title,favIconUrl
`;
    const rows = windowData.tabs
      .map(({ title, url, favIconUrl }) => `${url},${title},${favIconUrl}`)
      .join(`
`);
    return header + rows;
  }

  /**
   * Exports tab data as Markdown.
   * @param {{ tabs: Array<{title: string, url: string, favIconUrl: string}> }} windowData
   * @returns {string} Markdown string of tab data.
   */
  toMarkdown(windowData) {
    return windowData.tabs
      .map(({ title, url }) => `[${title}](${url})`)
      .join(`
`);
  }

  /**
   * Exports all windows' tab data as HTML.
   * @param {Array<{ id: number, tabs: Array<{title: string, url: string, favIconUrl: string}> }>} windowsData
   * @returns {string} HTML string of all windows' tab data.
   */
  toHTML(windowsData) {
    const docStart = `<!DOCTYPE html>
		<html lang="en">
		<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<title>Tabs at ${new Date().toLocaleString("default", { dateStyle: "full" })}</title>
		</head>
		<body>
		`;

    /**
     * Generates an HTML list item for a tab.
     * @param {{title: string, url: string}} tab
     * @returns {string}
     */
    const tabListItem = (tab) => `<li style="padding-block: 0.2em">
            <a href="${tab.url}" target="_blank" rel="noopener noreferrer">${tab.title}</a>
        </li>`;

    /**
     * Generates an HTML details block for a window.
     * @param {{id: number, tabs: Array}} window
     * @returns {string}
     */
    const windowDetails = (window) => `<details>
            <summary>${this.winTitles[window.id] ?? window.id}</summary>
            <ol>
                ${window.tabs.map(tabListItem).join("")}
            </ol>
        </details>`;

    return `${docStart}<blockquote >
            <h1>${new Date().toLocaleString("default", { dateStyle: "full" })}</h1>
            ${windowsData.map(windowDetails).join(`
`)}
        </blockquote></body></html>`;
  }

  /**
   * Main export method. Gets tab/window data and triggers download in the selected format.
   * @param {"json"|"csv"|"markdown"|"html"} format
   * @returns {Promise<void>}
   */
  async export(format) {
    // Get all normal windows with tabs populated
    const windows = await chrome.windows.get(windowId, {
      windowTypes: ["normal"],
      populate: true,
    });
    // Get window titles for HTML export
    this.winTitles = await getWindowTitles(null);
    // Generate export data in the selected format
    const data =
      format === "json"
        ? this.toJSON(windows)
        : format === "csv"
        ? this.toCSV(windows)
        : format === "markdown"
        ? this.toMarkdown(windows)
        : this.toHTML(windows);
    this.downloadFile(data, format);
  }

  /**
   * Triggers a download of the given data as a file.
   * @param {string} data
   * @param {string} format
   */
  downloadFile(data, format) {
    const blob = new Blob([data], { type: "text/plain" });
    const anchor = document.createElement("a");
    anchor.setAttribute("href", URL.createObjectURL(blob));
    anchor.setAttribute("download", new Date().toISOString() + "." + format);
    anchor.click();
  }
}

/**
 * Custom element for the import/export dialog.
 * Handles UI and user interactions for importing and exporting tabs.
 */
class ImportExportDialogElement extends HTMLElement {
  constructor() {
    super();
  }

  /**
   * Handles file upload for importing tabs.
   * @param {Event} event
   */
  onFileUpload(event) {
    // Regex to match URLs starting with https://
    const urlRegex = /https:\/\/[^\s,"')\n]+/g;
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.onload = async (loadEvent) => {
      const text = loadEvent.target.result;
      if (typeof text !== "string") return;
      const matches = text.matchAll(urlRegex);
      for (const match of matches) {
        const url = match[0];
        if (url && URL.canParse(url)) {
          // Create tab in window and discard it after a delay
          const { id: tabId } = await createTabInWindow({ url, windowId });
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await chrome.tabs.discard(tabId);
        }
      }
    };
    reader.onerror = (error) => console.error(error);
    reader.readAsText(file);
  }

  /**
   * Handles export button click.
   */
  exportTabs() {
    const format = this.querySelector("select").value;
    new TabExportUtility().export(format);
  }

  /**
   * Handles tab switching in the dialog.
   * @param {{ target: HTMLInputElement }} param0
   */
  switchTab({ target }) {
    this.querySelector("#" + target.value).scrollIntoView();
  }

  /**
   * Renders the dialog UI.
   * @returns {any} Template result (depends on html tagged template implementation)
   */
  render() {
    return html`<header @change=${this.switchTab.bind(this)}>
				<label>${i18n("import")} <input type="radio" name="import-export" value="import-tab" hidden /> </label>
				<label
					>${i18n("export")} <input type="radio" name="import-export" value="export-tab" hidden checked />
				</label>
			</header>
			<div>
				<section id="import-tab">
					<input type="file" accept="text/html,text/csv,application/json" @change=${this.onFileUpload} />
					<button>${i18n("import")}</button>
				</section>
				<section id="export-tab">
					<label>
						<span>${i18n("format")}</span>
						<select name="format">
							<option value="html">html</option>
							<option value="json">json</option>
							<option value="markdown">markdown</option>
							<option value="csv">csv</option>
							<option value="sql">SQL</option>
						</select>
					</label>
					<button @click=${this.exportTabs.bind(this)}>Export</button>
				</section>
			</div> `;
  }

  /**
   * Lifecycle callback when element is added to the DOM.
   */
  connectedCallback() {
    this.style.top = "import-export";
    this.setAttribute("popover", "");
    this.replaceChildren(this.render());
    this.showPopover();
    this.querySelector("#export-tab").scrollIntoView();
  }
}

// Register the custom element
customElements.define("import-export-dialog", ImportExportDialogElement);

/** Export the dialog element class for external use. */
export { ImportExportDialogElement as ImportExportDialog };
